import { createSupabaseServerClient } from "@/lib/supabase/server";
import { redirect } from "next/navigation";
import { Container } from "@chakra-ui/react";
import SpaceDetail from "@/components/spaces/SpaceDetail";
import { getUserProfileById } from "@/lib/supabase/queries";
import type {
  ProfileRow,
  SpaceWithCommunityInfo,
  SpaceMemberRow,
  CommunityMemberRow,
  PostWithDetails,
} from "@/types/app.types"; 

export default async function SpacePage({
  params,
}: {
  params: { spaceId: string };
}) {
  const supabase = createSupabaseServerClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (!session?.user?.id) { 
    redirect("/login"); 
  }

  const { data: space, error: spaceError } = await supabase
    .from("spaces")
    .select("*, community:communities(id, name, slug, owner_id)") // Ensure owner_id is fetched for community
    .eq("id", params.spaceId)
    .returns<SpaceWithCommunityInfo | null>() 
    .single();

  if (spaceError) {
    console.error("Error fetching space:", spaceError);
    redirect("/dashboard"); 
  }

  if (!space) {
    console.error(`Space with id ${params.spaceId} not found, redirecting.`);
    redirect("/dashboard"); 
  }

  const [profileResult, spaceMemberResult, communityMemberResultCond] = await Promise.all([
    getUserProfileById(supabase, session.user.id),
    supabase
      .from("space_members")
      .select("id") 
      .eq("space_id", params.spaceId)
      .eq("profile_id", session.user.id)
      .returns<Pick<SpaceMemberRow, 'id'> | null>() 
      .single(),
    space.community?.id 
      ? supabase
          .from("community_members")
          .select("id") 
          .eq("community_id", space.community.id)
          .eq("profile_id", session.user.id)
          .returns<Pick<CommunityMemberRow, 'id'> | null>()
          .single()
      : Promise.resolve({ data: null, error: null })
  ]);

  const { data: profile, error: profileError } = profileResult;
  const { data: spaceMember, error: spaceMemberError } = spaceMemberResult;
  const { data: communityMember, error: communityMemberError } = communityMemberResultCond;
  
  if (profileError) console.error("Error fetching profile:", profileError);
  if (spaceMemberError) console.error("Error fetching space member:", spaceMemberError);
  if (communityMemberError) console.error("Error fetching community member:", communityMemberError);
  
  if (space.is_private === true && !spaceMember && !communityMember) {
    redirect("/dashboard"); 
  }

  const { data: posts, error: postsError } = await supabase
    .from("content_modules")
    .select(
      "*, author:profiles(id, full_name, avatar_url), reactions_count:content_reactions(count), comments_count:content_comments(count)"
    )
    .eq("space_id", params.spaceId)
    .eq("type", "post") 
    .order("created_at", { ascending: false })
    .returns<PostWithDetails[]>(); 

  if (postsError) {
    console.error("Error fetching posts:", postsError);
  }

  return (
    <Container maxW="container.xl" py={{base: 6, md: 10}}>
      <SpaceDetail
        space={space} 
        posts={posts || []}
        currentUser={profile} 
        isMember={!!spaceMember || !!communityMember}
      />
    </Container>
  );
}
