import { createSupabaseServerClient } from "@/lib/supabase/server";
import { redirect } from "next/navigation";
import { Container } from "@chakra-ui/react";
import PostDetail from "@/components/spaces/PostDetail";

export default async function PostPage({
  params,
}: {
  params: { spaceId: string; postId: string };
}) {
  const supabase = createSupabaseServerClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (!session) {
    redirect("/login");
  }

  // Get post details with author
  const { data: post } = await supabase
    .from("content_modules")
    .select(
      `
      *,
      author:profiles(*),
      space:spaces(id, name, is_private, community_id)
    `,
    )
    .eq("id", params.postId)
    .single();

  if (!post || post.space_id !== params.spaceId) {
    redirect(`/spaces/${params.spaceId}`);
  }

  // Check if user is a member of the space or community
  const { data: spaceMember } = await supabase
    .from("space_members")
    .select("*")
    .eq("space_id", params.spaceId)
    .eq("profile_id", session.user.id)
    .single();

  const { data: communityMember } = await supabase
    .from("community_members")
    .select("*")
    .eq("community_id", post.space.community_id)
    .eq("profile_id", session.user.id)
    .single();

  // If space is private and user is not a member, redirect
  if (post.space.is_private && !spaceMember && !communityMember) {
    redirect("/dashboard");
  }

  // Get user profile
  const { data: profile } = await supabase
    .from("profiles")
    .select("*")
    .eq("id", session.user.id)
    .single();

  // Get comments for this post
  const { data: comments } = await supabase
    .from("content_comments")
    .select(
      `
      *,
      author:profiles(*)
    `,
    )
    .eq("content_id", params.postId)
    .order("created_at", { ascending: true });

  // Get reactions for this post
  const { data: reactions } = await supabase
    .from("content_reactions")
    .select("*")
    .eq("content_id", params.postId);

  // Check if current user has reacted
  const userReaction = reactions?.find((r) => r.profile_id === session.user.id);

  return (
    <Container maxW="container.xl" py={6}>
      <PostDetail
        post={post}
        comments={comments || []}
        reactions={reactions || []}
        currentUser={profile}
        userReaction={userReaction}
        isMember={!!spaceMember || !!communityMember}
      />
    </Container>
  );
}
