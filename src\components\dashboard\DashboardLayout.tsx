"use client";

import { ReactNode, useState } from "react";
import {
  Box,
  Flex,
  Text,
  Button,
  Avatar,
  VStack,
  HStack,
  IconButton,
  Heading,
  Link,
} from "@chakra-ui/react";
import { useDisclosure } from "@/components/ui/disclosure";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>er<PERSON>eader, Drawer<PERSON><PERSON>lay, <PERSON>er<PERSON>ontent, DrawerCloseButton } from "@/components/ui/drawer";
import { useDirection } from "@/lib/contexts/DirectionContext";
import { useTranslation } from "react-i18next";
import { Menu, Home, Users, Settings, MessageSquare } from "lucide-react";
import LanguageSwitcher from "@/components/common/LanguageSwitcher";

interface DashboardLayoutProps {
  children: ReactNode;
  user: any;
}

export default function DashboardLayout({
  children,
  user,
}: DashboardLayoutProps) {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { direction } = useDirection();
  // Temporarily bypass translation issues
  const getText = (key: string, fallback: string) => {
    return fallback;
  };

  const SidebarContent = () => (
    <VStack align="stretch" spacing={0} h="full">
      <Box p={6} borderBottomWidth="1px">
        <Heading size="lg" color="teal.600">
          Scenius
        </Heading>
      </Box>
      <VStack align="stretch" spacing={1} p={4} flex={1}>
        <Link href="/dashboard" _hover={{ textDecoration: "none" }}>
          <Button
            variant="ghost"
            leftIcon={<Home size={20} />}
            justifyContent="flex-start"
            w="full"
          >
            {getText("nav.dashboard", "Dashboard")}
          </Button>
        </Link>
        <Link href="/communities" _hover={{ textDecoration: "none" }}>
          <Button
            variant="ghost"
            leftIcon={<Users size={20} />}
            justifyContent="flex-start"
            w="full"
          >
            {getText("nav.communities", "Communities")}
          </Button>
        </Link>
        <Link href="/spaces" _hover={{ textDecoration: "none" }}>
          <Button
            variant="ghost"
            leftIcon={<MessageSquare size={20} />}
            justifyContent="flex-start"
            w="full"
          >
            {getText("nav.spaces", "Spaces")}
          </Button>
        </Link>
        <Link href="/settings" _hover={{ textDecoration: "none" }}>
          <Button
            variant="ghost"
            leftIcon={<Settings size={20} />}
            justifyContent="flex-start"
            w="full"
          >
            {getText("nav.settings", "Settings")}
          </Button>
        </Link>
      </VStack>
    </VStack>
  );

  return (
    <Box minH="100vh" bg="gray.50" dir={direction}>
      {/* Desktop Sidebar */}
      <Box
        display={{ base: "none", md: "block" }}
        position="fixed"
        left={0}
        top={0}
        w="240px"
        h="100vh"
        bg="white"
        borderRightWidth="1px"
        borderColor="gray.200"
      >
        <SidebarContent />
      </Box>

      {/* Mobile Drawer */}
      <Drawer isOpen={isOpen} placement="left" onClose={onClose}>
        <DrawerOverlay />
        <DrawerContent>
          <DrawerCloseButton />
          <DrawerHeader>
            <Heading size="lg" color="teal.600">
              Scenius
            </Heading>
          </DrawerHeader>
          <DrawerBody p={0}>
            <SidebarContent />
          </DrawerBody>
        </DrawerContent>
      </Drawer>

      {/* Main Content */}
      <Box ml={{ base: 0, md: "240px" }}>
        {/* Top Navbar */}
        <Flex
          bg="white"
          borderBottomWidth="1px"
          borderColor="gray.200"
          px={4}
          py={4}
          align="center"
          justify="space-between"
        >
          <IconButton
            display={{ base: "flex", md: "none" }}
            onClick={onOpen}
            variant="ghost"
            aria-label="Open menu"
            icon={<Menu size={20} />}
          />
          <HStack spacing={4} ml="auto">
            <LanguageSwitcher />
            <Text fontSize="sm" color="gray.600">
              {getText("dashboard.welcome", "Welcome")}, {user?.full_name || getText("dashboard.user", "User")}
            </Text>
            <Avatar
              size="sm"
              name={user?.full_name || "User"}
              src={user?.avatar_url}
              bg="teal.600"
            />
          </HStack>
        </Flex>

        {/* Page Content */}
        <Box p={4}>
          {children}
        </Box>
      </Box>
    </Box>
  );
}
