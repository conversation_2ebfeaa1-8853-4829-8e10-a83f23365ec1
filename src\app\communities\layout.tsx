import { createSupabaseServerClient } from "@/lib/supabase/server";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import { getUserProfileById } from "@/lib/supabase/queries"; 
import type { Database } from "@/types/supabase";

type ProfileRow = Database['public']['Tables']['profiles']['Row'];

export default async function CommunitiesLayoutWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = createSupabaseServerClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();

  let profile: ProfileRow | null = null;
  
  if (session?.user?.id) {
    const { data: fetchedProfile, error: profileError } = await getUserProfileById(supabase, session.user.id);
    if (profileError) {
      console.error("Error fetching profile in communities layout:", profileError);
    }
    profile = fetchedProfile;
  } else {
    console.warn("Communities layout: No session or user ID found.");
  }

  return <DashboardLayout user={profile}>{children}</DashboardLayout>;
}
