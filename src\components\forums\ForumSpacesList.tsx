"use client";

import { useState } from "react";
import {
  Box,
  Heading,
  Text,
  SimpleGrid,
  Card,
  CardBody,
  Flex,
  Icon,
  Badge,
  Link,
} from "@chakra-ui/react";
import { Input, InputGroup, InputLeftElement } from "@/components/ui/input";
import { useColorModeValue } from "@/components/ui/color-mode";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";
import NextLink from "next/link";
import { useDirection } from "@/lib/contexts/DirectionContext";
import { Search, MessageCircle, Lock, Globe } from "lucide-react";
import type { ProfileRow, SpaceWithCountsAndCommunity } from "../../types/app.types";

interface ForumSpacesListProps {
  forumSpaces: SpaceWithCountsAndCommunity[];
  currentUser: ProfileRow | null; // Retained currentUser as it might be used for conditional UI later
}

export default function ForumSpacesList({
  forumSpaces,
  // currentUser, // Currently unused, but kept in props for potential future use
}: ForumSpacesListProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const { direction } = useDirection();
  const [searchQuery, setSearchQuery] = useState("");

  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  const filteredSpaces = forumSpaces.filter((space) => {
    if (!searchQuery) return true;
    const nameMatch = space.name?.toLowerCase().includes(searchQuery.toLowerCase());
    const descriptionMatch = space.description &&
      space.description.toLowerCase().includes(searchQuery.toLowerCase());
    return nameMatch || descriptionMatch;
  });

  return (
    <Box dir={direction}>
      <Box mb={{base: 6, md: 8}}>
        <Heading as="h1" size={{base: "lg", md: "xl"}} mb={2}>
          {t("forums.title")}
        </Heading>
        <Text color="gray.600" fontSize={{base: "md", md: "lg"}}>{t("forums.description")}</Text>
      </Box>

      <InputGroup mb={{base: 4, md: 6}}>
        <InputLeftElement pointerEvents="none">
          <Search color="gray.300" />
        </InputLeftElement>
        <Input
          placeholder={t("forums.searchPlaceholder")}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          size="lg"
        />
      </InputGroup>

      {filteredSpaces.length > 0 ? (
        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={{base: 4, md: 6}}>
          {filteredSpaces.map((space) => (
            <Card
              key={space.id}
              bg={bgColor}
              borderColor={borderColor}
              borderWidth="1px"
              cursor="pointer"
              onClick={() => router.push(`/spaces/${space.id}`)}
              _hover={{ transform: "translateY(-4px)", shadow: "lg" }}
              transition="all 0.2s ease-in-out"
              shadow="sm"
            >
              <CardBody p={{base: 4, md: 5}} display="flex" flexDirection="column"> {/* Ensure CardBody is flex column */}
                <Flex justify="space-between" align="flex-start" mb={2}>
                  <Heading as="h3" size="md" mb={2} lineClamp={2}>
                    <Link as={NextLink} href={`/spaces/${space.id}`} _hover={{textDecoration: 'none'}}>
                      {space.name}
                    </Link>
                  </Heading>
                  <Badge
                    colorPalette={space.is_private ? "red" : "green"}
                    display="flex"
                    alignItems="center"
                    variant="subtle"
                    px={2} py={0.5}
                    flexShrink={0} // Prevent badge from shrinking
                  >
                    <Icon
                      as={space.is_private ? Lock : Globe}
                      boxSize={3}
                      mr={1}
                    />
                    {space.is_private ? t("space.private") : t("space.public")}
                  </Badge>
                </Flex>

                <Text lineClamp={2} mb={4} color="gray.600" fontSize="sm" flexGrow={1}> {/* Allow description to grow */}
                  {space.description}
                </Text>

                <Flex justify="space-between" align="center" mt="auto">
                  <Flex align="center" gap={2}>
                    <Icon as={MessageCircle} boxSize={4} color="gray.500" />
                    <Text fontSize="sm" color="gray.500">
                      {space.posts_count || 0} {t("forums.posts")}
                    </Text>
                  </Flex>

                  {space.community && (
                    <Text fontSize="xs" color="gray.500" lineClamp={1} title={space.community.name ?? ""}>
                      {space.community.name}
                    </Text>
                  )}
                </Flex>
              </CardBody>
            </Card>
          ))}
        </SimpleGrid>
      ) : (
        <Box
          textAlign="center"
          py={{base: 8, md: 10}}
          borderWidth="1px"
          borderRadius="lg"
          borderColor={borderColor}
          bg={bgColor}
        >
          <Text fontSize={{base: "md", md: "lg"}}>
            {searchQuery ? t("forums.noSearchResults") : t("forums.noForums")}
          </Text>
        </Box>
      )}
    </Box>
  );
}
