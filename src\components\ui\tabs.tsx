"use client"

import { Tabs as ChakraTabs } from "@chakra-ui/react"
import { forwardRef } from "react"

export interface TabsProps extends ChakraTabs.RootProps {}

export const Tabs = forwardRef<HTMLDivElement, TabsProps>(
  function Tabs(props, ref) {
    return <ChakraTabs.Root ref={ref} {...props} />
  },
)

export interface TabListProps extends ChakraTabs.ListProps {}

export const TabList = forwardRef<HTMLDivElement, TabListProps>(
  function TabList(props, ref) {
    return <ChakraTabs.List ref={ref} {...props} />
  },
)

export interface TabProps extends ChakraTabs.TriggerProps {}

export const Tab = forwardRef<HTMLButtonElement, TabProps>(
  function Tab(props, ref) {
    return <ChakraTabs.Trigger ref={ref} {...props} />
  },
)

export interface TabPanelsProps extends ChakraTabs.ContentGroupProps {}

export const TabPanels = forwardRef<HTMLDivElement, TabPanelsProps>(
  function TabPanels(props, ref) {
    return <ChakraTabs.ContentGroup ref={ref} {...props} />
  },
)

export interface TabPanelProps extends ChakraTabs.ContentProps {}

export const TabPanel = forwardRef<HTMLDivElement, TabPanelProps>(
  function TabPanel(props, ref) {
    return <ChakraTabs.Content ref={ref} {...props} />
  },
)
