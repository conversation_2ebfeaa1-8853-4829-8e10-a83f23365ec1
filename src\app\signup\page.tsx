import { Box, Container, Flex, Heading, Text } from "@chakra-ui/react";
import AuthForm from "@/components/auth/AuthForm";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
export const dynamic = 'force-dynamic'; 

export default function SignupPage() { 
  return (
    <>
      <Header isAuthenticated={false} /> {/* Pass isAuthenticated if <PERSON><PERSON> expects it */}
      <Container maxW="container.xl" py={{base: 6, md: 10}}>
        <Flex direction="column" align="center" justify="center" minH="70vh">
          <Box mb={{base: 6, md: 8}} textAlign="center">
            <Heading as="h1" size={{base: "xl", md: "2xl"}} mb={{base: 1, md: 2}} color="wuilt.product">
              Scenius.community
            </Heading>
            <Text fontSize={{base: "lg", md: "xl"}} color="gray.600">
              Join our community platform
            </Text>
          </Box>
          <AuthForm mode="signUp" />
        </Flex>
      </Container>
      <Footer />
    </>
  );
}
