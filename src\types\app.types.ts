import type { Database } from './supabase';

export type ProfileRow = Database['public']['Tables']['profiles']['Row'];
export type SpaceRow = Database['public']['Tables']['spaces']['Row'];
export type CommunityRow = Database['public']['Tables']['communities']['Row'];
export type ContentModuleRow = Database['public']['Tables']['content_modules']['Row'];
export type SpaceMemberRow = Database['public']['Tables']['space_members']['Row'];
export type CommunityMemberRow = Database['public']['Tables']['community_members']['Row'];
// export type ContentReactionRow = Database['public']['Tables']['content_reactions']['Row']; // Not used yet
// export type ContentCommentRow = Database['public']['Tables']['content_comments']['Row']; // Not used yet

export type CommunitySlimForSpace = Pick<CommunityRow, 'id' | 'name' | 'slug' | 'owner_id'>;

export interface SpaceWithCommunityInfo extends SpaceRow {
  community: CommunitySlimForSpace | null;
}

export type AuthorProfileForPost = Pick<ProfileRow, 'id' | 'full_name' | 'avatar_url'> | null;

export interface PostWithDetails extends ContentModuleRow {
  author: AuthorProfileForPost;
  reactions_count: number; // Assuming these counts are added by the query
  comments_count: number;  // Assuming these counts are added by the query
}

export type CommunityForDashboardView = Pick<CommunityRow,
  'id' | 'name' | 'description' | 'logo_url' | 'banner_url' | 'is_private' | 'slug'
>;

export interface SpaceWithCountsAndCommunity extends SpaceRow {
  community: Pick<CommunityRow, 'name' | 'slug'> | null;
  posts_count: number;
}
