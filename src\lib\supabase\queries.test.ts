import { getUserProfileById } from './queries';
// SupabaseClient type might be needed for more complex mocks, but here jest.fn() suffices for the chain
// import type { SupabaseClient } from '@supabase/supabase-js'; 

const mockProfileData = {
  id: 'test-user-id',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  full_name: 'Test User',
  avatar_url: 'https://example.com/avatar.png',
  bio: 'Test bio',
  website: 'https://example.com',
  language: 'en',
  username: 'testuser',
};

describe('getUserProfileById', () => {
  const mockSingle = jest.fn();
  const mockEq = jest.fn(() => ({ single: mockSingle }));
  const mockSelect = jest.fn(() => ({ eq: mockEq }));
  const mockFrom = jest.fn(() => ({ select: mockSelect }));

  const mockSupabaseClient: any = { // Using 'any' for simplicity in mock setup
    from: mockFrom,
  };

  beforeEach(() => {
    mockFrom.mockClear();
    mockSelect.mockClear();
    mockEq.mockClear();
    mockSingle.mockClear();
  });

  test('should return profile data on successful fetch', async () => {
    mockSingle.mockResolvedValueOnce({ data: mockProfileData, error: null });
    const userId = 'test-user-id';
    const { data, error } = await getUserProfileById(mockSupabaseClient, userId);
    expect(data).toEqual(mockProfileData);
    expect(error).toBeNull();
    expect(mockFrom).toHaveBeenCalledWith('profiles');
    expect(mockSelect).toHaveBeenCalledWith('*');
    expect(mockEq).toHaveBeenCalledWith('id', userId);
    expect(mockSingle).toHaveBeenCalledTimes(1);
  });

  test('should return an error if the fetch fails', async () => {
    const mockError = { message: 'Fetch failed', code: '500' };
    mockSingle.mockResolvedValueOnce({ data: null, error: mockError });
    const userId = 'another-user-id';
    const { data, error } = await getUserProfileById(mockSupabaseClient, userId);
    expect(data).toBeNull();
    expect(error).toEqual(mockError);
  });

  test('should handle unexpected errors during the query chain', async () => {
    const mockChainError = new Error('Unexpected error in query chain');
    mockEq.mockImplementationOnce(() => { throw mockChainError; });
    const userId = 'error-chain-user-id';
    await expect(getUserProfileById(mockSupabaseClient, userId)).rejects.toThrow(mockChainError);
    expect(mockSingle).not.toHaveBeenCalled();
  });
});
