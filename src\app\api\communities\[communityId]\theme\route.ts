import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextResponse } from 'next/server';
import type { Database } from '../../../../../types/supabase'; // Path for nested routes

type CommunityUpdate = Database['public']['Tables']['communities']['Update'];

export async function PUT(
  request: Request,
  { params }: { params: { communityId: string } }
) {
  const supabase = createSupabaseServerClient();
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { communityId } = params;
  if (!communityId) {
    return NextResponse.json({ error: 'Community ID is required' }, { status: 400 });
  }

  let parsedBody: { theme?: CommunityUpdate['theme'] };
  try {
    parsedBody = await request.json();
    if (typeof parsedBody.theme !== 'object' || parsedBody.theme === null) {
      return NextResponse.json({ error: 'Theme data must be an object and provided under "theme" key' }, { status: 400 });
    }
  } catch (e) {
    return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
  }

  const themeToUpdate: Pick<CommunityUpdate, 'theme'> = { theme: parsedBody.theme };

  const { data: updatedCommunity, error: updateError } = await supabase
    .from('communities')
    .update(themeToUpdate)
    .eq('id', communityId)
    .eq('owner_id', user.id) 
    .select()
    .single();

  if (updateError) {
    console.error('Error updating community theme:', updateError);
    if (updateError.code === 'PGRST116') { 
      return NextResponse.json({ error: 'Community not found or user not authorized to update' }, { status: 404 });
    }
    return NextResponse.json({ error: 'Failed to update community theme', details: updateError.message }, { status: 500 });
  }

  if (!updatedCommunity) { 
    return NextResponse.json({ error: 'Community not found after update or user not authorized' }, { status: 404 });
  }

  return NextResponse.json(updatedCommunity);
}
