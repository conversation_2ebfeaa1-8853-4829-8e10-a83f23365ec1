[05:16:38.570] Running build in Washington, D.C., USA (East) – iad1
[05:16:38.570] Build machine configuration: 2 cores, 8 GB
[05:16:38.584] Cloning github.com/meto002/123 (Branch: main, Commit: f912078)
[05:16:38.904] Previous build caches not available
[05:16:39.345] Cloning completed: 761.000ms
[05:16:39.644] Running "vercel build"
[05:16:40.088] Vercel CLI 42.1.1
[05:16:40.414] Installing dependencies...
[05:16:44.841] npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
[05:16:45.443] npm warn deprecated domexception@4.0.0: Use your platform's native DOMException instead
[05:16:45.629] npm warn deprecated abab@2.0.6: Use your platform's native atob() and btoa() methods instead
[05:16:47.114] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[05:16:47.310] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[05:16:47.384] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[05:16:47.463] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[05:16:47.898] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[05:16:58.040] 
[05:16:58.041] added 952 packages in 17s
[05:16:58.041] 
[05:16:58.041] 124 packages are looking for funding
[05:16:58.041]   run `npm fund` for details
[05:16:58.240] Detected Next.js version: 14.2.23
[05:16:58.246] Running "npm run build"
[05:16:58.481] 
[05:16:58.481] > build
[05:16:58.481] > next build
[05:16:58.482] 
[05:16:59.558] Attention: Next.js now collects completely anonymous telemetry regarding usage.
[05:16:59.558] This information is used to shape Next.js' roadmap and prioritize features.
[05:16:59.558] You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[05:16:59.558] https://nextjs.org/telemetry
[05:16:59.559] 
[05:16:59.684]   ▲ Next.js 14.2.23
[05:16:59.684] 
[05:16:59.945]    Creating an optimized production build ...
[05:17:26.746] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:26.747] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:26.748] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:26.748] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:26.748] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:26.749] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:26.749] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:26.749] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:26.750] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:26.750] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:26.750] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:26.750] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:26.764] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:26.766] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:26.767] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:26.767] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:27.555] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:27.555] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:27.557] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:27.557] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:27.557] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:27.557] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:27.557] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:27.558] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:27.558] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:27.559] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:27.559] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:27.559] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:27.647] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:27.647] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:27.647] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:27.647] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:30.211]  ⚠ Compiled with warnings
[05:17:30.212] 
[05:17:30.212] ./src/app/communities/new/page.tsx
[05:17:30.212] Attempted import error: 'AlertIcon' is not exported from '@chakra-ui/react' (imported as 'AlertIcon').
[05:17:30.212] 
[05:17:30.213] Import trace for requested module:
[05:17:30.213] ./src/app/communities/new/page.tsx
[05:17:30.213] 
[05:17:30.213] ./src/app/communities/new/page.tsx
[05:17:30.213] Attempted import error: 'FormControl' is not exported from '@chakra-ui/react' (imported as 'FormControl').
[05:17:30.213] 
[05:17:30.213] Import trace for requested module:
[05:17:30.213] ./src/app/communities/new/page.tsx
[05:17:30.214] 
[05:17:30.214] ./src/app/communities/new/page.tsx
[05:17:30.214] Attempted import error: 'FormLabel' is not exported from '@chakra-ui/react' (imported as 'FormLabel').
[05:17:30.214] 
[05:17:30.214] Import trace for requested module:
[05:17:30.214] ./src/app/communities/new/page.tsx
[05:17:30.214] 
[05:17:30.215] ./src/app/communities/new/page.tsx
[05:17:30.215] Attempted import error: 'FormControl' is not exported from '@chakra-ui/react' (imported as 'FormControl').
[05:17:30.215] 
[05:17:30.215] Import trace for requested module:
[05:17:30.215] ./src/app/communities/new/page.tsx
[05:17:30.215] 
[05:17:30.216] ./src/app/communities/new/page.tsx
[05:17:30.216] Attempted import error: 'FormLabel' is not exported from '@chakra-ui/react' (imported as 'FormLabel').
[05:17:30.216] 
[05:17:30.216] Import trace for requested module:
[05:17:30.216] ./src/app/communities/new/page.tsx
[05:17:30.216] 
[05:17:30.216] ./src/app/communities/new/page.tsx
[05:17:30.216] Attempted import error: 'FormHelperText' is not exported from '@chakra-ui/react' (imported as 'FormHelperText').
[05:17:30.217] 
[05:17:30.217] Import trace for requested module:
[05:17:30.217] ./src/app/communities/new/page.tsx
[05:17:30.217] 
[05:17:30.217] ./src/app/communities/new/page.tsx
[05:17:30.217] Attempted import error: 'FormControl' is not exported from '@chakra-ui/react' (imported as 'FormControl').
[05:17:30.217] 
[05:17:30.218] Import trace for requested module:
[05:17:30.218] ./src/app/communities/new/page.tsx
[05:17:30.218] 
[05:17:30.218] ./src/app/communities/new/page.tsx
[05:17:30.218] Attempted import error: 'FormLabel' is not exported from '@chakra-ui/react' (imported as 'FormLabel').
[05:17:30.218] 
[05:17:30.218] Import trace for requested module:
[05:17:30.219] ./src/app/communities/new/page.tsx
[05:17:30.219] 
[05:17:30.219] ./src/app/communities/new/page.tsx
[05:17:30.219] Attempted import error: 'Radio' is not exported from '@chakra-ui/react' (imported as 'Radio').
[05:17:30.219] 
[05:17:30.219] Import trace for requested module:
[05:17:30.219] ./src/app/communities/new/page.tsx
[05:17:30.220] 
[05:17:30.220] ./src/app/communities/new/page.tsx
[05:17:30.220] Attempted import error: 'Radio' is not exported from '@chakra-ui/react' (imported as 'Radio').
[05:17:30.220] 
[05:17:30.220] Import trace for requested module:
[05:17:30.221] ./src/app/communities/new/page.tsx
[05:17:30.221] 
[05:17:30.221] ./src/app/communities/new/page.tsx
[05:17:30.221] Attempted import error: 'FormControl' is not exported from '@chakra-ui/react' (imported as 'FormControl').
[05:17:30.221] 
[05:17:30.221] Import trace for requested module:
[05:17:30.221] ./src/app/communities/new/page.tsx
[05:17:30.222] 
[05:17:30.222] ./src/app/communities/new/page.tsx
[05:17:30.222] Attempted import error: 'FormLabel' is not exported from '@chakra-ui/react' (imported as 'FormLabel').
[05:17:30.222] 
[05:17:30.222] Import trace for requested module:
[05:17:30.222] ./src/app/communities/new/page.tsx
[05:17:30.222] 
[05:17:30.223] ./src/app/communities/new/page.tsx
[05:17:30.223] Attempted import error: 'FormHelperText' is not exported from '@chakra-ui/react' (imported as 'FormHelperText').
[05:17:30.223] 
[05:17:30.223] Import trace for requested module:
[05:17:30.223] ./src/app/communities/new/page.tsx
[05:17:30.223] 
[05:17:30.224] ./src/components/forums/ForumSpacesList.tsx
[05:17:30.224] Attempted import error: 'useColorModeValue' is not exported from '@chakra-ui/react' (imported as 'useColorModeValue').
[05:17:30.224] 
[05:17:30.224] Import trace for requested module:
[05:17:30.224] ./src/components/forums/ForumSpacesList.tsx
[05:17:30.224] 
[05:17:30.225] ./src/components/forums/ForumSpacesList.tsx
[05:17:30.225] Attempted import error: 'useColorModeValue' is not exported from '@chakra-ui/react' (imported as 'useColorModeValue').
[05:17:30.225] 
[05:17:30.225] Import trace for requested module:
[05:17:30.225] ./src/components/forums/ForumSpacesList.tsx
[05:17:30.225] 
[05:17:30.226] ./src/components/forums/ForumSpacesList.tsx
[05:17:30.226] Attempted import error: 'InputLeftElement' is not exported from '@chakra-ui/react' (imported as 'InputLeftElement').
[05:17:30.226] 
[05:17:30.226] Import trace for requested module:
[05:17:30.226] ./src/components/forums/ForumSpacesList.tsx
[05:17:30.226] 
[05:17:30.227] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.227] Attempted import error: 'useToast' is not exported from '@chakra-ui/react' (imported as 'useToast').
[05:17:30.227] 
[05:17:30.227] Import trace for requested module:
[05:17:30.228] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.228] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.228] 
[05:17:30.228] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.228] Attempted import error: 'Modal' is not exported from '@chakra-ui/react' (imported as 'Modal').
[05:17:30.228] 
[05:17:30.229] Import trace for requested module:
[05:17:30.229] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.229] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.229] 
[05:17:30.229] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.229] Attempted import error: 'ModalOverlay' is not exported from '@chakra-ui/react' (imported as 'ModalOverlay').
[05:17:30.230] 
[05:17:30.230] Import trace for requested module:
[05:17:30.230] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.231] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.231] 
[05:17:30.231] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.231] Attempted import error: 'ModalContent' is not exported from '@chakra-ui/react' (imported as 'ModalContent').
[05:17:30.231] 
[05:17:30.231] Import trace for requested module:
[05:17:30.232] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.232] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.232] 
[05:17:30.232] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.232] Attempted import error: 'ModalHeader' is not exported from '@chakra-ui/react' (imported as 'ModalHeader').
[05:17:30.232] 
[05:17:30.232] Import trace for requested module:
[05:17:30.232] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.232] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.232] 
[05:17:30.232] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.232] Attempted import error: 'ModalCloseButton' is not exported from '@chakra-ui/react' (imported as 'ModalCloseButton').
[05:17:30.232] 
[05:17:30.232] Import trace for requested module:
[05:17:30.233] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.233] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.233] 
[05:17:30.233] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.233] Attempted import error: 'ModalBody' is not exported from '@chakra-ui/react' (imported as 'ModalBody').
[05:17:30.233] 
[05:17:30.233] Import trace for requested module:
[05:17:30.238] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.238] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.238] 
[05:17:30.238] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.239] Attempted import error: 'FormControl' is not exported from '@chakra-ui/react' (imported as 'FormControl').
[05:17:30.239] 
[05:17:30.239] Import trace for requested module:
[05:17:30.239] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.239] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.239] 
[05:17:30.239] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.239] Attempted import error: 'FormLabel' is not exported from '@chakra-ui/react' (imported as 'FormLabel').
[05:17:30.239] 
[05:17:30.239] Import trace for requested module:
[05:17:30.239] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.239] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.239] 
[05:17:30.239] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.239] Attempted import error: 'FormErrorMessage' is not exported from '@chakra-ui/react' (imported as 'FormErrorMessage').
[05:17:30.240] 
[05:17:30.240] Import trace for requested module:
[05:17:30.240] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.240] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.240] 
[05:17:30.240] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.240] Attempted import error: 'FormControl' is not exported from '@chakra-ui/react' (imported as 'FormControl').
[05:17:30.240] 
[05:17:30.240] Import trace for requested module:
[05:17:30.240] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.240] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.240] 
[05:17:30.240] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.240] Attempted import error: 'FormLabel' is not exported from '@chakra-ui/react' (imported as 'FormLabel').
[05:17:30.240] 
[05:17:30.241] Import trace for requested module:
[05:17:30.241] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.241] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.241] 
[05:17:30.241] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.241] Attempted import error: 'FormErrorMessage' is not exported from '@chakra-ui/react' (imported as 'FormErrorMessage').
[05:17:30.241] 
[05:17:30.241] Import trace for requested module:
[05:17:30.241] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.241] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.241] 
[05:17:30.241] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.241] Attempted import error: 'FormControl' is not exported from '@chakra-ui/react' (imported as 'FormControl').
[05:17:30.241] 
[05:17:30.241] Import trace for requested module:
[05:17:30.242] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.242] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.242] 
[05:17:30.242] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.242] Attempted import error: 'FormLabel' is not exported from '@chakra-ui/react' (imported as 'FormLabel').
[05:17:30.242] 
[05:17:30.242] Import trace for requested module:
[05:17:30.242] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.242] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.242] 
[05:17:30.242] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.242] Attempted import error: 'ModalFooter' is not exported from '@chakra-ui/react' (imported as 'ModalFooter').
[05:17:30.242] 
[05:17:30.242] Import trace for requested module:
[05:17:30.242] ./src/components/spaces/CreatePostModal.tsx
[05:17:30.243] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.243] 
[05:17:30.243] ./src/components/spaces/PostDetail.tsx
[05:17:30.244] Attempted import error: 'useToast' is not exported from '@chakra-ui/react' (imported as 'useToast').
[05:17:30.244] 
[05:17:30.244] Import trace for requested module:
[05:17:30.244] ./src/components/spaces/PostDetail.tsx
[05:17:30.244] 
[05:17:30.244] ./src/components/spaces/PostDetail.tsx
[05:17:30.244] Attempted import error: 'useColorModeValue' is not exported from '@chakra-ui/react' (imported as 'useColorModeValue').
[05:17:30.244] 
[05:17:30.244] Import trace for requested module:
[05:17:30.244] ./src/components/spaces/PostDetail.tsx
[05:17:30.244] 
[05:17:30.244] ./src/components/spaces/PostDetail.tsx
[05:17:30.244] Attempted import error: 'useColorModeValue' is not exported from '@chakra-ui/react' (imported as 'useColorModeValue').
[05:17:30.244] 
[05:17:30.244] Import trace for requested module:
[05:17:30.244] ./src/components/spaces/PostDetail.tsx
[05:17:30.245] 
[05:17:30.245] ./src/components/spaces/PostDetail.tsx
[05:17:30.245] Attempted import error: 'MenuButton' is not exported from '@chakra-ui/react' (imported as 'MenuButton').
[05:17:30.245] 
[05:17:30.245] Import trace for requested module:
[05:17:30.246] ./src/components/spaces/PostDetail.tsx
[05:17:30.246] 
[05:17:30.246] ./src/components/spaces/PostDetail.tsx
[05:17:30.246] Attempted import error: 'MenuList' is not exported from '@chakra-ui/react' (imported as 'MenuList').
[05:17:30.246] 
[05:17:30.246] Import trace for requested module:
[05:17:30.246] ./src/components/spaces/PostDetail.tsx
[05:17:30.246] 
[05:17:30.246] ./src/components/spaces/PostDetail.tsx
[05:17:30.246] Attempted import error: 'Divider' is not exported from '@chakra-ui/react' (imported as 'Divider').
[05:17:30.246] 
[05:17:30.247] Import trace for requested module:
[05:17:30.247] ./src/components/spaces/PostDetail.tsx
[05:17:30.247] 
[05:17:30.247] ./src/components/spaces/PostDetail.tsx
[05:17:30.247] Attempted import error: 'useColorModeValue' is not exported from '@chakra-ui/react' (imported as 'useColorModeValue').
[05:17:30.247] 
[05:17:30.247] Import trace for requested module:
[05:17:30.247] ./src/components/spaces/PostDetail.tsx
[05:17:30.247] 
[05:17:30.247] ./src/components/spaces/PostDetail.tsx
[05:17:30.247] Attempted import error: 'MenuButton' is not exported from '@chakra-ui/react' (imported as 'MenuButton').
[05:17:30.247] 
[05:17:30.247] Import trace for requested module:
[05:17:30.247] ./src/components/spaces/PostDetail.tsx
[05:17:30.247] 
[05:17:30.247] ./src/components/spaces/PostDetail.tsx
[05:17:30.247] Attempted import error: 'MenuList' is not exported from '@chakra-ui/react' (imported as 'MenuList').
[05:17:30.248] 
[05:17:30.248] Import trace for requested module:
[05:17:30.248] ./src/components/spaces/PostDetail.tsx
[05:17:30.248] 
[05:17:30.248] ./src/components/spaces/PostsList.tsx
[05:17:30.248] Attempted import error: 'useColorModeValue' is not exported from '@chakra-ui/react' (imported as 'useColorModeValue').
[05:17:30.248] 
[05:17:30.248] Import trace for requested module:
[05:17:30.248] ./src/components/spaces/PostsList.tsx
[05:17:30.248] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.248] 
[05:17:30.248] ./src/components/spaces/PostsList.tsx
[05:17:30.248] Attempted import error: 'useColorModeValue' is not exported from '@chakra-ui/react' (imported as 'useColorModeValue').
[05:17:30.248] 
[05:17:30.248] Import trace for requested module:
[05:17:30.248] ./src/components/spaces/PostsList.tsx
[05:17:30.248] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.248] 
[05:17:30.248] ./src/components/spaces/PostsList.tsx
[05:17:30.248] Attempted import error: 'MenuButton' is not exported from '@chakra-ui/react' (imported as 'MenuButton').
[05:17:30.249] 
[05:17:30.249] Import trace for requested module:
[05:17:30.249] ./src/components/spaces/PostsList.tsx
[05:17:30.249] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.249] 
[05:17:30.249] ./src/components/spaces/PostsList.tsx
[05:17:30.249] Attempted import error: 'MenuList' is not exported from '@chakra-ui/react' (imported as 'MenuList').
[05:17:30.249] 
[05:17:30.249] Import trace for requested module:
[05:17:30.249] ./src/components/spaces/PostsList.tsx
[05:17:30.249] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.249] 
[05:17:30.249] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.249] Attempted import error: 'useColorModeValue' is not exported from '@chakra-ui/react' (imported as 'useColorModeValue').
[05:17:30.249] 
[05:17:30.249] Import trace for requested module:
[05:17:30.249] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.249] 
[05:17:30.249] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.249] Attempted import error: 'useColorModeValue' is not exported from '@chakra-ui/react' (imported as 'useColorModeValue').
[05:17:30.250] 
[05:17:30.250] Import trace for requested module:
[05:17:30.250] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.250] 
[05:17:30.250] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.250] Attempted import error: 'useColorModeValue' is not exported from '@chakra-ui/react' (imported as 'useColorModeValue').
[05:17:30.250] 
[05:17:30.250] Import trace for requested module:
[05:17:30.250] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.250] 
[05:17:30.250] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.250] Attempted import error: 'TabList' is not exported from '@chakra-ui/react' (imported as 'TabList').
[05:17:30.250] 
[05:17:30.250] Import trace for requested module:
[05:17:30.250] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.251] 
[05:17:30.251] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.251] Attempted import error: 'Tab' is not exported from '@chakra-ui/react' (imported as 'Tab').
[05:17:30.251] 
[05:17:30.251] Import trace for requested module:
[05:17:30.251] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.251] 
[05:17:30.251] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.251] Attempted import error: 'Tab' is not exported from '@chakra-ui/react' (imported as 'Tab').
[05:17:30.251] 
[05:17:30.251] Import trace for requested module:
[05:17:30.251] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.251] 
[05:17:30.251] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.252] Attempted import error: 'Tab' is not exported from '@chakra-ui/react' (imported as 'Tab').
[05:17:30.252] 
[05:17:30.252] Import trace for requested module:
[05:17:30.252] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.252] 
[05:17:30.252] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.252] Attempted import error: 'Tab' is not exported from '@chakra-ui/react' (imported as 'Tab').
[05:17:30.252] 
[05:17:30.252] Import trace for requested module:
[05:17:30.252] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.252] 
[05:17:30.252] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.252] Attempted import error: 'TabPanels' is not exported from '@chakra-ui/react' (imported as 'TabPanels').
[05:17:30.252] 
[05:17:30.252] Import trace for requested module:
[05:17:30.253] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.253] 
[05:17:30.253] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.253] Attempted import error: 'TabPanel' is not exported from '@chakra-ui/react' (imported as 'TabPanel').
[05:17:30.253] 
[05:17:30.253] Import trace for requested module:
[05:17:30.253] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.253] 
[05:17:30.253] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.253] Attempted import error: 'TabPanel' is not exported from '@chakra-ui/react' (imported as 'TabPanel').
[05:17:30.253] 
[05:17:30.253] Import trace for requested module:
[05:17:30.253] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.253] 
[05:17:30.253] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.254] Attempted import error: 'TabPanel' is not exported from '@chakra-ui/react' (imported as 'TabPanel').
[05:17:30.254] 
[05:17:30.254] Import trace for requested module:
[05:17:30.254] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.254] 
[05:17:30.254] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.255] Attempted import error: 'TabPanel' is not exported from '@chakra-ui/react' (imported as 'TabPanel').
[05:17:30.255] 
[05:17:30.255] Import trace for requested module:
[05:17:30.255] ./src/components/spaces/SpaceDetail.tsx
[05:17:30.255] 
[05:17:52.536] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:52.536] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:52.538] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:52.538] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:52.543] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:52.543] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:52.543] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:52.543] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:52.620] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:52.621] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:52.622] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:52.622] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:52.627] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:52.629] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:52.629] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:52.630] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[05:17:54.114]  ⚠ Compiled with warnings
[05:17:54.114] 
[05:17:54.115] ./src/app/communities/new/page.tsx
[05:17:54.115] Attempted import error: 'AlertIcon' is not exported from '@chakra-ui/react' (imported as 'AlertIcon').
[05:17:54.115] 
[05:17:54.115] Import trace for requested module:
[05:17:54.115] ./src/app/communities/new/page.tsx
[05:17:54.115] 
[05:17:54.115] ./src/app/communities/new/page.tsx
[05:17:54.116] Attempted import error: 'FormControl' is not exported from '@chakra-ui/react' (imported as 'FormControl').
[05:17:54.116] 
[05:17:54.116] Import trace for requested module:
[05:17:54.116] ./src/app/communities/new/page.tsx
[05:17:54.116] 
[05:17:54.116] ./src/app/communities/new/page.tsx
[05:17:54.116] Attempted import error: 'FormLabel' is not exported from '@chakra-ui/react' (imported as 'FormLabel').
[05:17:54.116] 
[05:17:54.116] Import trace for requested module:
[05:17:54.117] ./src/app/communities/new/page.tsx
[05:17:54.117] 
[05:17:54.117] ./src/app/communities/new/page.tsx
[05:17:54.117] Attempted import error: 'FormControl' is not exported from '@chakra-ui/react' (imported as 'FormControl').
[05:17:54.117] 
[05:17:54.117] Import trace for requested module:
[05:17:54.117] ./src/app/communities/new/page.tsx
[05:17:54.117] 
[05:17:54.117] ./src/app/communities/new/page.tsx
[05:17:54.118] Attempted import error: 'FormLabel' is not exported from '@chakra-ui/react' (imported as 'FormLabel').
[05:17:54.118] 
[05:17:54.118] Import trace for requested module:
[05:17:54.118] ./src/app/communities/new/page.tsx
[05:17:54.118] 
[05:17:54.118] ./src/app/communities/new/page.tsx
[05:17:54.118] Attempted import error: 'FormHelperText' is not exported from '@chakra-ui/react' (imported as 'FormHelperText').
[05:17:54.118] 
[05:17:54.119] Import trace for requested module:
[05:17:54.119] ./src/app/communities/new/page.tsx
[05:17:54.119] 
[05:17:54.119] ./src/app/communities/new/page.tsx
[05:17:54.119] Attempted import error: 'FormControl' is not exported from '@chakra-ui/react' (imported as 'FormControl').
[05:17:54.119] 
[05:17:54.119] Import trace for requested module:
[05:17:54.120] ./src/app/communities/new/page.tsx
[05:17:54.120] 
[05:17:54.120] ./src/app/communities/new/page.tsx
[05:17:54.120] Attempted import error: 'FormLabel' is not exported from '@chakra-ui/react' (imported as 'FormLabel').
[05:17:54.120] 
[05:17:54.120] Import trace for requested module:
[05:17:54.121] ./src/app/communities/new/page.tsx
[05:17:54.121] 
[05:17:54.121] ./src/app/communities/new/page.tsx
[05:17:54.121] Attempted import error: 'Radio' is not exported from '@chakra-ui/react' (imported as 'Radio').
[05:17:54.121] 
[05:17:54.121] Import trace for requested module:
[05:17:54.122] ./src/app/communities/new/page.tsx
[05:17:54.123] 
[05:17:54.123] ./src/app/communities/new/page.tsx
[05:17:54.123] Attempted import error: 'Radio' is not exported from '@chakra-ui/react' (imported as 'Radio').
[05:17:54.123] 
[05:17:54.123] Import trace for requested module:
[05:17:54.123] ./src/app/communities/new/page.tsx
[05:17:54.124] 
[05:17:54.124] ./src/app/communities/new/page.tsx
[05:17:54.124] Attempted import error: 'FormControl' is not exported from '@chakra-ui/react' (imported as 'FormControl').
[05:17:54.124] 
[05:17:54.124] Import trace for requested module:
[05:17:54.124] ./src/app/communities/new/page.tsx
[05:17:54.126] 
[05:17:54.126] ./src/app/communities/new/page.tsx
[05:17:54.126] Attempted import error: 'FormLabel' is not exported from '@chakra-ui/react' (imported as 'FormLabel').
[05:17:54.126] 
[05:17:54.126] Import trace for requested module:
[05:17:54.127] ./src/app/communities/new/page.tsx
[05:17:54.127] 
[05:17:54.127] ./src/app/communities/new/page.tsx
[05:17:54.127] Attempted import error: 'FormHelperText' is not exported from '@chakra-ui/react' (imported as 'FormHelperText').
[05:17:54.127] 
[05:17:54.127] Import trace for requested module:
[05:17:54.127] ./src/app/communities/new/page.tsx
[05:17:54.127] 
[05:17:54.128] ./src/components/forums/ForumSpacesList.tsx
[05:17:54.128] Attempted import error: 'useColorModeValue' is not exported from '@chakra-ui/react' (imported as 'useColorModeValue').
[05:17:54.128] 
[05:17:54.128] Import trace for requested module:
[05:17:54.128] ./src/components/forums/ForumSpacesList.tsx
[05:17:54.128] 
[05:17:54.129] ./src/components/forums/ForumSpacesList.tsx
[05:17:54.129] Attempted import error: 'useColorModeValue' is not exported from '@chakra-ui/react' (imported as 'useColorModeValue').
[05:17:54.129] 
[05:17:54.129] Import trace for requested module:
[05:17:54.129] ./src/components/forums/ForumSpacesList.tsx
[05:17:54.129] 
[05:17:54.130] ./src/components/forums/ForumSpacesList.tsx
[05:17:54.130] Attempted import error: 'InputLeftElement' is not exported from '@chakra-ui/react' (imported as 'InputLeftElement').
[05:17:54.130] 
[05:17:54.130] Import trace for requested module:
[05:17:54.130] ./src/components/forums/ForumSpacesList.tsx
[05:17:54.130] 
[05:17:54.131] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.131] Attempted import error: 'useToast' is not exported from '@chakra-ui/react' (imported as 'useToast').
[05:17:54.131] 
[05:17:54.131] Import trace for requested module:
[05:17:54.131] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.131] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.131] 
[05:17:54.131] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.132] Attempted import error: 'Modal' is not exported from '@chakra-ui/react' (imported as 'Modal').
[05:17:54.132] 
[05:17:54.132] Import trace for requested module:
[05:17:54.132] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.132] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.132] 
[05:17:54.132] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.132] Attempted import error: 'ModalOverlay' is not exported from '@chakra-ui/react' (imported as 'ModalOverlay').
[05:17:54.132] 
[05:17:54.133] Import trace for requested module:
[05:17:54.133] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.133] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.133] 
[05:17:54.133] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.133] Attempted import error: 'ModalContent' is not exported from '@chakra-ui/react' (imported as 'ModalContent').
[05:17:54.133] 
[05:17:54.133] Import trace for requested module:
[05:17:54.133] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.134] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.134] 
[05:17:54.134] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.134] Attempted import error: 'ModalHeader' is not exported from '@chakra-ui/react' (imported as 'ModalHeader').
[05:17:54.134] 
[05:17:54.134] Import trace for requested module:
[05:17:54.134] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.134] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.134] 
[05:17:54.135] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.135] Attempted import error: 'ModalCloseButton' is not exported from '@chakra-ui/react' (imported as 'ModalCloseButton').
[05:17:54.135] 
[05:17:54.135] Import trace for requested module:
[05:17:54.135] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.135] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.135] 
[05:17:54.135] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.136] Attempted import error: 'ModalBody' is not exported from '@chakra-ui/react' (imported as 'ModalBody').
[05:17:54.136] 
[05:17:54.136] Import trace for requested module:
[05:17:54.136] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.136] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.136] 
[05:17:54.136] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.136] Attempted import error: 'FormControl' is not exported from '@chakra-ui/react' (imported as 'FormControl').
[05:17:54.136] 
[05:17:54.137] Import trace for requested module:
[05:17:54.137] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.137] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.137] 
[05:17:54.137] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.137] Attempted import error: 'FormLabel' is not exported from '@chakra-ui/react' (imported as 'FormLabel').
[05:17:54.137] 
[05:17:54.137] Import trace for requested module:
[05:17:54.138] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.138] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.138] 
[05:17:54.138] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.138] Attempted import error: 'FormErrorMessage' is not exported from '@chakra-ui/react' (imported as 'FormErrorMessage').
[05:17:54.138] 
[05:17:54.138] Import trace for requested module:
[05:17:54.138] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.139] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.139] 
[05:17:54.139] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.139] Attempted import error: 'FormControl' is not exported from '@chakra-ui/react' (imported as 'FormControl').
[05:17:54.139] 
[05:17:54.139] Import trace for requested module:
[05:17:54.139] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.139] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.140] 
[05:17:54.140] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.141] Attempted import error: 'FormLabel' is not exported from '@chakra-ui/react' (imported as 'FormLabel').
[05:17:54.141] 
[05:17:54.141] Import trace for requested module:
[05:17:54.141] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.141] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.141] 
[05:17:54.141] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.142] Attempted import error: 'FormErrorMessage' is not exported from '@chakra-ui/react' (imported as 'FormErrorMessage').
[05:17:54.142] 
[05:17:54.142] Import trace for requested module:
[05:17:54.142] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.142] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.142] 
[05:17:54.142] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.142] Attempted import error: 'FormControl' is not exported from '@chakra-ui/react' (imported as 'FormControl').
[05:17:54.142] 
[05:17:54.143] Import trace for requested module:
[05:17:54.143] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.143] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.143] 
[05:17:54.143] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.143] Attempted import error: 'FormLabel' is not exported from '@chakra-ui/react' (imported as 'FormLabel').
[05:17:54.143] 
[05:17:54.144] Import trace for requested module:
[05:17:54.144] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.144] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.144] 
[05:17:54.144] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.144] Attempted import error: 'ModalFooter' is not exported from '@chakra-ui/react' (imported as 'ModalFooter').
[05:17:54.145] 
[05:17:54.145] Import trace for requested module:
[05:17:54.145] ./src/components/spaces/CreatePostModal.tsx
[05:17:54.145] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.145] 
[05:17:54.145] ./src/components/spaces/PostDetail.tsx
[05:17:54.145] Attempted import error: 'useToast' is not exported from '@chakra-ui/react' (imported as 'useToast').
[05:17:54.146] 
[05:17:54.146] Import trace for requested module:
[05:17:54.146] ./src/components/spaces/PostDetail.tsx
[05:17:54.146] 
[05:17:54.146] ./src/components/spaces/PostDetail.tsx
[05:17:54.146] Attempted import error: 'useColorModeValue' is not exported from '@chakra-ui/react' (imported as 'useColorModeValue').
[05:17:54.147] 
[05:17:54.147] Import trace for requested module:
[05:17:54.147] ./src/components/spaces/PostDetail.tsx
[05:17:54.147] 
[05:17:54.147] ./src/components/spaces/PostDetail.tsx
[05:17:54.147] Attempted import error: 'useColorModeValue' is not exported from '@chakra-ui/react' (imported as 'useColorModeValue').
[05:17:54.147] 
[05:17:54.147] Import trace for requested module:
[05:17:54.148] ./src/components/spaces/PostDetail.tsx
[05:17:54.148] 
[05:17:54.148] ./src/components/spaces/PostDetail.tsx
[05:17:54.148] Attempted import error: 'MenuButton' is not exported from '@chakra-ui/react' (imported as 'MenuButton').
[05:17:54.148] 
[05:17:54.148] Import trace for requested module:
[05:17:54.148] ./src/components/spaces/PostDetail.tsx
[05:17:54.148] 
[05:17:54.149] ./src/components/spaces/PostDetail.tsx
[05:17:54.149] Attempted import error: 'MenuList' is not exported from '@chakra-ui/react' (imported as 'MenuList').
[05:17:54.149] 
[05:17:54.149] Import trace for requested module:
[05:17:54.149] ./src/components/spaces/PostDetail.tsx
[05:17:54.149] 
[05:17:54.149] ./src/components/spaces/PostDetail.tsx
[05:17:54.150] Attempted import error: 'Divider' is not exported from '@chakra-ui/react' (imported as 'Divider').
[05:17:54.150] 
[05:17:54.150] Import trace for requested module:
[05:17:54.150] ./src/components/spaces/PostDetail.tsx
[05:17:54.150] 
[05:17:54.151] ./src/components/spaces/PostDetail.tsx
[05:17:54.151] Attempted import error: 'useColorModeValue' is not exported from '@chakra-ui/react' (imported as 'useColorModeValue').
[05:17:54.152] 
[05:17:54.152] Import trace for requested module:
[05:17:54.152] ./src/components/spaces/PostDetail.tsx
[05:17:54.152] 
[05:17:54.152] ./src/components/spaces/PostDetail.tsx
[05:17:54.152] Attempted import error: 'MenuButton' is not exported from '@chakra-ui/react' (imported as 'MenuButton').
[05:17:54.152] 
[05:17:54.152] Import trace for requested module:
[05:17:54.152] ./src/components/spaces/PostDetail.tsx
[05:17:54.152] 
[05:17:54.152] ./src/components/spaces/PostDetail.tsx
[05:17:54.152] Attempted import error: 'MenuList' is not exported from '@chakra-ui/react' (imported as 'MenuList').
[05:17:54.152] 
[05:17:54.152] Import trace for requested module:
[05:17:54.152] ./src/components/spaces/PostDetail.tsx
[05:17:54.152] 
[05:17:54.152] ./src/components/spaces/PostsList.tsx
[05:17:54.152] Attempted import error: 'useColorModeValue' is not exported from '@chakra-ui/react' (imported as 'useColorModeValue').
[05:17:54.152] 
[05:17:54.153] Import trace for requested module:
[05:17:54.153] ./src/components/spaces/PostsList.tsx
[05:17:54.153] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.153] 
[05:17:54.153] ./src/components/spaces/PostsList.tsx
[05:17:54.153] Attempted import error: 'useColorModeValue' is not exported from '@chakra-ui/react' (imported as 'useColorModeValue').
[05:17:54.153] 
[05:17:54.153] Import trace for requested module:
[05:17:54.153] ./src/components/spaces/PostsList.tsx
[05:17:54.153] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.153] 
[05:17:54.153] ./src/components/spaces/PostsList.tsx
[05:17:54.153] Attempted import error: 'MenuButton' is not exported from '@chakra-ui/react' (imported as 'MenuButton').
[05:17:54.153] 
[05:17:54.153] Import trace for requested module:
[05:17:54.153] ./src/components/spaces/PostsList.tsx
[05:17:54.153] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.153] 
[05:17:54.153] ./src/components/spaces/PostsList.tsx
[05:17:54.154] Attempted import error: 'MenuList' is not exported from '@chakra-ui/react' (imported as 'MenuList').
[05:17:54.154] 
[05:17:54.154] Import trace for requested module:
[05:17:54.154] ./src/components/spaces/PostsList.tsx
[05:17:54.154] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.154] 
[05:17:54.154] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.154] Attempted import error: 'useColorModeValue' is not exported from '@chakra-ui/react' (imported as 'useColorModeValue').
[05:17:54.154] 
[05:17:54.154] Import trace for requested module:
[05:17:54.154] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.154] 
[05:17:54.154] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.154] Attempted import error: 'useColorModeValue' is not exported from '@chakra-ui/react' (imported as 'useColorModeValue').
[05:17:54.154] 
[05:17:54.154] Import trace for requested module:
[05:17:54.154] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.154] 
[05:17:54.156] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.156] Attempted import error: 'useColorModeValue' is not exported from '@chakra-ui/react' (imported as 'useColorModeValue').
[05:17:54.156] 
[05:17:54.156] Import trace for requested module:
[05:17:54.156] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.156] 
[05:17:54.156] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.156] Attempted import error: 'TabList' is not exported from '@chakra-ui/react' (imported as 'TabList').
[05:17:54.156] 
[05:17:54.156] Import trace for requested module:
[05:17:54.156] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.156] 
[05:17:54.156] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.156] Attempted import error: 'Tab' is not exported from '@chakra-ui/react' (imported as 'Tab').
[05:17:54.156] 
[05:17:54.156] Import trace for requested module:
[05:17:54.156] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.156] 
[05:17:54.156] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.156] Attempted import error: 'Tab' is not exported from '@chakra-ui/react' (imported as 'Tab').
[05:17:54.156] 
[05:17:54.157] Import trace for requested module:
[05:17:54.157] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.157] 
[05:17:54.157] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.157] Attempted import error: 'Tab' is not exported from '@chakra-ui/react' (imported as 'Tab').
[05:17:54.157] 
[05:17:54.157] Import trace for requested module:
[05:17:54.157] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.157] 
[05:17:54.157] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.157] Attempted import error: 'Tab' is not exported from '@chakra-ui/react' (imported as 'Tab').
[05:17:54.157] 
[05:17:54.157] Import trace for requested module:
[05:17:54.157] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.157] 
[05:17:54.157] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.157] Attempted import error: 'TabPanels' is not exported from '@chakra-ui/react' (imported as 'TabPanels').
[05:17:54.157] 
[05:17:54.157] Import trace for requested module:
[05:17:54.157] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.158] 
[05:17:54.158] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.158] Attempted import error: 'TabPanel' is not exported from '@chakra-ui/react' (imported as 'TabPanel').
[05:17:54.158] 
[05:17:54.158] Import trace for requested module:
[05:17:54.158] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.158] 
[05:17:54.158] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.158] Attempted import error: 'TabPanel' is not exported from '@chakra-ui/react' (imported as 'TabPanel').
[05:17:54.158] 
[05:17:54.158] Import trace for requested module:
[05:17:54.158] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.158] 
[05:17:54.158] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.158] Attempted import error: 'TabPanel' is not exported from '@chakra-ui/react' (imported as 'TabPanel').
[05:17:54.158] 
[05:17:54.158] Import trace for requested module:
[05:17:54.158] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.158] 
[05:17:54.158] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.158] Attempted import error: 'TabPanel' is not exported from '@chakra-ui/react' (imported as 'TabPanel').
[05:17:54.158] 
[05:17:54.158] Import trace for requested module:
[05:17:54.158] ./src/components/spaces/SpaceDetail.tsx
[05:17:54.159] 
[05:17:54.269]  ✓ Compiled successfully
[05:17:54.281]    Linting and checking validity of types ...
[05:18:07.511] Failed to compile.
[05:18:07.512] 
[05:18:07.512] ./src/app/communities/new/page.tsx:8:3
[05:18:07.513] Type error: Module '"@chakra-ui/react"' has no exported member 'FormControl'.
[05:18:07.513] 
[05:18:07.513] [0m [90m  6 |[39m   [33mButton[39m[33m,[39m[0m
[05:18:07.513] [0m [90m  7 |[39m   [33mContainer[39m[33m,[39m[0m
[05:18:07.513] [0m[31m[1m>[22m[39m[90m  8 |[39m   [33mFormControl[39m[33m,[39m[0m
[05:18:07.513] [0m [90m    |[39m   [31m[1m^[22m[39m[0m
[05:18:07.514] [0m [90m  9 |[39m   [33mFormErrorMessage[39m[33m,[39m[0m
[05:18:07.514] [0m [90m 10 |[39m   [33mFormHelperText[39m[33m,[39m[0m
[05:18:07.514] [0m [90m 11 |[39m   [33mFormLabel[39m[33m,[39m[0m
[05:18:07.550] Static worker exited with code: 1 and signal: null
[05:18:07.571] Error: Command "npm run build" exited with 1
[05:18:07.887] 