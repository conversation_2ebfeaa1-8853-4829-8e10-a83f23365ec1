[06:14:33.775] Running build in Washington, D.C., USA (East) – iad1
[06:14:33.776] Build machine configuration: 2 cores, 8 GB
[06:14:33.814] Cloning github.com/meto002/123 (Branch: main, Commit: c5767dc)
[06:14:33.979] Previous build caches not available
[06:14:34.286] Cloning completed: 472.000ms
[06:14:34.626] Running "vercel build"
[06:14:35.076] Vercel CLI 42.1.1
[06:14:36.492] Installing dependencies...
[06:14:41.414] npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
[06:14:41.952] npm warn deprecated domexception@4.0.0: Use your platform's native DOMException instead
[06:14:42.266] npm warn deprecated abab@2.0.6: Use your platform's native atob() and btoa() methods instead
[06:14:43.851] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[06:14:43.989] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[06:14:44.072] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[06:14:44.109] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[06:14:44.584] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[06:14:54.679] 
[06:14:54.680] added 952 packages in 18s
[06:14:54.680] 
[06:14:54.681] 124 packages are looking for funding
[06:14:54.681]   run `npm fund` for details
[06:14:54.728] Detected Next.js version: 14.2.23
[06:14:54.738] Running "npm run build"
[06:14:54.847] 
[06:14:54.847] > build
[06:14:54.848] > next build
[06:14:54.848] 
[06:14:55.388] Attention: Next.js now collects completely anonymous telemetry regarding usage.
[06:14:55.389] This information is used to shape Next.js' roadmap and prioritize features.
[06:14:55.389] You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[06:14:55.389] https://nextjs.org/telemetry
[06:14:55.389] 
[06:14:55.440]   ▲ Next.js 14.2.23
[06:14:55.440] 
[06:14:55.506]    Creating an optimized production build ...
[06:15:17.140] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.141] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.141] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.142] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.142] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.142] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.142] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.142] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.142] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.142] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.142] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.142] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.154] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.154] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.154] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.154] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.272] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.272] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.272] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.272] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.272] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.272] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.272] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.272] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.273] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.273] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.273] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.273] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (152kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.281] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.281] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.281] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:17.281] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:40.363] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:40.364] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:40.364] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:40.364] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:40.372] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:40.372] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:40.372] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:40.373] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:40.441] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:40.441] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:40.441] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:40.441] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:40.455] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:40.455] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:40.455] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:40.456] <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (153kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:15:41.834]  ✓ Compiled successfully
[06:15:41.835]    Linting and checking validity of types ...
[06:15:54.706] Failed to compile.
[06:15:54.706] 
[06:15:54.706] ./src/app/spaces/[spaceId]/page.tsx:54:11
[06:15:54.706] Type error: Property 'community' does not exist on type 'never'.
[06:15:54.707] 
[06:15:54.707] [0m [90m 52 |[39m       [33m.[39mreturns[33m<[39m[33mPick[39m[33m<[39m[33mSpaceMemberRow[39m[33m,[39m [32m'id'[39m[33m>[39m [33m|[39m [36mnull[39m[33m>[39m() [0m
[06:15:54.707] [0m [90m 53 |[39m       [33m.[39msingle()[33m,[39m[0m
[06:15:54.707] [0m[31m[1m>[22m[39m[90m 54 |[39m     space[33m.[39mcommunity[33m?[39m[33m.[39mid [0m
[06:15:54.707] [0m [90m    |[39m           [31m[1m^[22m[39m[0m
[06:15:54.707] [0m [90m 55 |[39m       [33m?[39m supabase[0m
[06:15:54.707] [0m [90m 56 |[39m           [33m.[39m[36mfrom[39m([32m"community_members"[39m)[0m
[06:15:54.707] [0m [90m 57 |[39m           [33m.[39mselect([32m"id"[39m) [0m
[06:15:54.738] Static worker exited with code: 1 and signal: null
[06:15:54.756] Error: Command "npm run build" exited with 1
[06:15:55.051] 
[06:15:58.202] Exiting build container