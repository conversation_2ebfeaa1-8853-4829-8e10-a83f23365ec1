import { render, screen, fireEvent } from '@testing-library/react';
import LanguageSwitcher from './LanguageSwitcher';
import { useTranslation } from 'react-i18next';
import { useDirection } from '@/lib/contexts/DirectionContext';
import { ChakraProvider } from '@chakra-ui/react';

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn(),
}));
jest.mock('@/lib/contexts/DirectionContext', () => ({
  useDirection: jest.fn(),
}));
jest.mock('@chakra-ui/react', () => {
  const originalChakra = jest.requireActual('@chakra-ui/react');
  return {
    ...originalChakra,
    useColorModeValue: jest.fn().mockImplementation((light, dark) => light),
  };
});

describe('LanguageSwitcher', () => {
  const mockChangeLanguage = jest.fn();
  const mockSetDirection = jest.fn();

  beforeEach(() => {
    mockChangeLanguage.mockClear();
    mockSetDirection.mockClear();
    (useTranslation as jest.Mock).mockReturnValue({
      i18n: { changeLanguage: mockChangeLanguage, language: 'en' },
      t: (str: string) => str,
    });
    (useDirection as jest.Mock).mockReturnValue({
      direction: 'ltr',
      setDirection: mockSetDirection,
    });
  });

  const renderWithProviders = (ui: React.ReactElement) => {
    return render(<ChakraProvider>{ui}</ChakraProvider>);
  };

  test('renders without crashing and shows language buttons', () => {
    renderWithProviders(<LanguageSwitcher />);
    expect(screen.getByText('English')).toBeInTheDocument();
    expect(screen.getByText('العربية')).toBeInTheDocument();
  });

  test('clicking "العربية" button calls changeLanguage with "ar" and setDirection with "rtl"', () => {
    renderWithProviders(<LanguageSwitcher />);
    fireEvent.click(screen.getByText('العربية'));
    expect(mockChangeLanguage).toHaveBeenCalledWith('ar');
    expect(mockSetDirection).toHaveBeenCalledWith('rtl');
  });

  test('clicking "English" button calls changeLanguage with "en" and setDirection with "ltr"', () => {
    (useTranslation as jest.Mock).mockReturnValueOnce({
      i18n: { changeLanguage: mockChangeLanguage, language: 'ar' },
      t: (str: string) => str,
    });
    (useDirection as jest.Mock).mockReturnValueOnce({
      direction: 'rtl',
      setDirection: mockSetDirection,
    });
    renderWithProviders(<LanguageSwitcher />);
    fireEvent.click(screen.getByText('English'));
    expect(mockChangeLanguage).toHaveBeenCalledWith('en');
    expect(mockSetDirection).toHaveBeenCalledWith('ltr');
  });
  
  test('English button is active when language is English', () => {
    (useTranslation as jest.Mock).mockReturnValue({
      i18n: { changeLanguage: mockChangeLanguage, language: 'en' },
      t: (str: string) => str,
    });
    renderWithProviders(<LanguageSwitcher />);
    expect(screen.getByText('English')).toHaveAttribute('data-active');
    expect(screen.getByText('العربية')).not.toHaveAttribute('data-active');
  });

  test('Arabic button is active when language is Arabic', () => {
    (useTranslation as jest.Mock).mockReturnValue({
      i18n: { changeLanguage: mockChangeLanguage, language: 'ar' },
      t: (str: string) => str,
    });
    renderWithProviders(<LanguageSwitcher />);
    expect(screen.getByText('العربية')).toHaveAttribute('data-active');
    expect(screen.getByText('English')).not.toHaveAttribute('data-active');
  });
});
