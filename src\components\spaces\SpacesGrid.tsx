"use client";

import {
  Simple<PERSON>rid,
  Box,
  Flex,
  Button,
  Icon,
  Heading,
  Text,
} from "@chakra-ui/react";
import { useColorModeValue } from "@/lib/utils/colorMode";
import { MessageSquare, FileText, BookOpen } from "lucide-react";

interface Space {
  id: string;
  type: string;
  name: string;
  description?: string;
}

interface SpacesGridProps {
  spaces: Space[];
}

function SpaceCard({ icon, title, description, count }: {
  icon: any;
  title: string;
  description: string;
  count: number;
}) {
  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  return (
    <Box
      p={6}
      borderWidth="1px"
      borderRadius="lg"
      bg={bgColor}
      borderColor={borderColor}
      cursor="pointer"
      _hover={{ borderColor: "teal.500" }}
      transition="all 0.2s"
    >
      <Flex align="center" mb={3}>
        <Icon as={icon} boxSize={6} mr={3} color="teal.500" />
        <Heading size="md">{title}</Heading>
      </Flex>
      <Text color="gray.600" mb={4}>
        {description}
      </Text>
      <Flex justify="space-between" align="center">
        <Text fontWeight="bold">{count} spaces</Text>
        <Button size="sm" colorScheme="teal" variant="outline">
          View All
        </Button>
      </Flex>
    </Box>
  );
}

export default function SpacesGrid({ spaces }: SpacesGridProps) {
  return (
    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
      {/* Forum Space Card */}
      <SpaceCard
        icon={MessageSquare}
        title="Forum"
        description="Discussion threads and conversations"
        count={spaces.filter((s) => s.type === "forum").length}
      />

      {/* Blog Space Card */}
      <SpaceCard
        icon={FileText}
        title="Blog"
        description="Articles and long-form content"
        count={spaces.filter((s) => s.type === "blog").length}
      />

      {/* Course Space Card */}
      <SpaceCard
        icon={BookOpen}
        title="Course"
        description="Structured learning content"
        count={spaces.filter((s) => s.type === "course").length}
      />
    </SimpleGrid>
  );
}
