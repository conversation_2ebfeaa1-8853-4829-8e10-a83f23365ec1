"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

type Direction = "ltr" | "rtl";

interface DirectionContextType {
  direction: Direction;
  setDirection: (direction: Direction) => void;
  toggleDirection: () => void;
}

const DirectionContext = createContext<DirectionContextType | undefined>(
  undefined,
);

export const DirectionProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { i18n } = useTranslation();
  const [direction, setDirection] = useState<Direction>("ltr");

  // Update direction when language changes
  useEffect(() => {
    const dir = i18n.language === "ar" ? "rtl" : "ltr";
    setDirection(dir);
    document.documentElement.dir = dir;
    document.documentElement.lang = i18n.language;
  }, [i18n.language]);

  const toggleDirection = () => {
    const newDirection = direction === "ltr" ? "rtl" : "ltr";
    setDirection(newDirection);
    document.documentElement.dir = newDirection;
    // Also change the language when toggling direction
    i18n.changeLanguage(newDirection === "rtl" ? "ar" : "en");
  };

  return (
    <DirectionContext.Provider
      value={{ direction, setDirection, toggleDirection }}
    >
      {children}
    </DirectionContext.Provider>
  );
};

export const useDirection = (): DirectionContextType => {
  const context = useContext(DirectionContext);
  if (context === undefined) {
    throw new Error("useDirection must be used within a DirectionProvider");
  }
  return context;
};
