"use client"

import { Field } from "@chakra-ui/react"
import { forwardRef } from "react"

export interface FormControlProps extends Field.RootProps {}

export const FormControl = forwardRef<HTMLDivElement, FormControlProps>(
  function FormControl(props, ref) {
    return <Field.Root ref={ref} {...props} />
  },
)

export interface FormLabelProps extends Field.LabelProps {}

export const FormLabel = forwardRef<HTMLLabelElement, FormLabelProps>(
  function FormLabel(props, ref) {
    return <Field.Label ref={ref} {...props} />
  },
)

export interface FormHelperTextProps extends Field.HelperTextProps {}

export const FormHelperText = forwardRef<HTMLDivElement, FormHelperTextProps>(
  function FormHelperText(props, ref) {
    return <Field.HelperText ref={ref} {...props} />
  },
)

export interface FormErrorMessageProps extends Field.ErrorTextProps {}

export const FormErrorMessage = forwardRef<HTMLDivElement, FormErrorMessageProps>(
  function FormErrorMessage(props, ref) {
    return <Field.ErrorText ref={ref} {...props} />
  },
)
