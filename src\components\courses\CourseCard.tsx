"use client";

import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ooter,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Clock, Users, BookOpen } from "lucide-react";
import { useTranslation } from "react-i18next";

interface CourseCardProps {
  id?: string;
  title?: string;
  description?: string;
  thumbnail?: string;
  instructor?: {
    name?: string;
    avatar?: string;
  };
  progress?: number;
  enrolled?: boolean;
  price?: number;
  currency?: string;
  duration?: string;
  studentsCount?: number;
  lessonsCount?: number;
  language?: "en" | "ar";
  isRTL?: boolean;
}

const CourseCard = ({
  id = "1",
  title = "Introduction to Web Development",
  description = "Learn the fundamentals of web development including HTML, CSS, and JavaScript.",
  thumbnail = "https://images.unsplash.com/photo-1587620962725-abab7fe55159?w=800&q=80",
  instructor = {
    name: "<PERSON>",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Sarah",
  },
  progress = 0,
  enrolled = false,
  price = 49.99,
  currency = "$",
  duration = "6 weeks",
  studentsCount = 1245,
  lessonsCount = 24,
  language = "en",
  isRTL = false,
}: CourseCardProps) => {
  // Initialize translation hook
  const { t } = useTranslation("common", { useSuspense: false });
  const directionClass = isRTL ? "rtl" : "ltr";

  return (
    <Card
      className={`w-[350px] h-[280px] overflow-hidden flex flex-col bg-white ${directionClass}`}
    >
      <div className="relative h-32">
        <img
          src={thumbnail}
          alt={title}
          className="w-full h-full object-cover"
        />
        {enrolled && (
          <div className="absolute top-2 right-2">
            <Badge
              variant="secondary"
              className="bg-yellow-100 text-yellow-800"
            >
              {language === "en" ? "Enrolled" : "مسجل"}
            </Badge>
          </div>
        )}
      </div>

      <CardHeader className="p-4 pb-0">
        <div className="flex items-start justify-between">
          <h3 className="text-lg font-semibold line-clamp-1">{title}</h3>
        </div>
      </CardHeader>

      <CardContent className="p-4 pt-2 flex-grow">
        <p className="text-sm text-gray-600 line-clamp-2 mb-2">{description}</p>

        <div className="flex items-center mt-2 text-sm text-gray-500">
          <Clock className="h-4 w-4 mr-1" />
          <span className="mr-3">{duration}</span>
          <Users className="h-4 w-4 mr-1" />
          <span className="mr-3">{studentsCount}</span>
          <BookOpen className="h-4 w-4 mr-1" />
          <span>
            {lessonsCount} {language === "en" ? "lessons" : "درس"}
          </span>
        </div>

        {enrolled && (
          <div className="mt-3">
            <div className="flex justify-between text-xs mb-1">
              <span>{language === "en" ? "Progress" : "التقدم"}</span>
              <span>{progress}%</span>
            </div>
            <Progress value={progress} className="h-1" />
          </div>
        )}
      </CardContent>

      <CardFooter className="p-4 pt-0 flex items-center justify-between">
        <div className="flex items-center">
          <Avatar className="h-6 w-6 mr-2">
            <AvatarImage src={instructor.avatar} alt={instructor.name} />
            <AvatarFallback>{instructor.name?.charAt(0)}</AvatarFallback>
          </Avatar>
          <span className="text-sm">{instructor.name}</span>
        </div>

        {!enrolled ? (
          <div className="font-semibold">
            {currency}
            {price}
          </div>
        ) : (
          <Button variant="outline" size="sm">
            {t("course.continue", "Continue")}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default CourseCard;
