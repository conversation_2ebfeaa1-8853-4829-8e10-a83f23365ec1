"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  Globe,
  Users,
  BookOpen,
  Bell,
  Settings,
  ChevronRight,
  ChevronLeft,
  Plus,
} from "lucide-react";
import CommunityCard from "@/components/community/CommunityCard";
import ContentFeed from "@/components/content/ContentFeed";
import CourseCard from "@/components/courses/CourseCard";

interface CommunityDashboardProps {
  userName?: string;
  language?: "en" | "ar";
  communities?: Array<{
    id: string;
    name: string;
    description: string;
    memberCount: number;
    thumbnail: string;
  }>;
  recentActivity?: Array<{
    id: string;
    type: string;
    title: string;
    author: string;
    authorAvatar: string;
    timestamp: string;
  }>;
  featuredContent?: Array<{
    id: string;
    type: string;
    title: string;
    description: string;
    thumbnail: string;
    author: string;
    authorAvatar: string;
  }>;
  courses?: Array<{
    id: string;
    title: string;
    description: string;
    instructor: string;
    instructorAvatar: string;
    progress?: number;
    enrolled?: boolean;
    price?: string;
    thumbnail: string;
  }>;
}

const CommunityDashboard: React.FC<CommunityDashboardProps> = ({
  userName = "John Doe",
  language = "en",
  communities = [
    {
      id: "1",
      name: "Web Development",
      description:
        "A community for web developers to share knowledge and resources.",
      memberCount: 1250,
      thumbnail:
        "https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800&q=80",
    },
    {
      id: "2",
      name: "UX Design",
      description:
        "Connect with UX designers and learn about user experience design.",
      memberCount: 850,
      thumbnail:
        "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=800&q=80",
    },
    {
      id: "3",
      name: "Data Science",
      description: "Explore data science concepts, tools, and techniques.",
      memberCount: 2100,
      thumbnail:
        "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&q=80",
    },
  ],
  recentActivity = [
    {
      id: "1",
      type: "post",
      title: "Introduction to React Hooks",
      author: "Sarah Johnson",
      authorAvatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=sarah",
      timestamp: "2 hours ago",
    },
    {
      id: "2",
      type: "comment",
      title: "Commented on 'CSS Grid vs Flexbox'",
      author: "Michael Chen",
      authorAvatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=michael",
      timestamp: "5 hours ago",
    },
    {
      id: "3",
      type: "course",
      title: "Completed 'JavaScript Fundamentals'",
      author: "Alex Rodriguez",
      authorAvatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=alex",
      timestamp: "1 day ago",
    },
  ],
  featuredContent = [
    {
      id: "1",
      type: "article",
      title: "Understanding TypeScript Generics",
      description:
        "Learn how to use TypeScript generics to create reusable components.",
      thumbnail:
        "https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&q=80",
      author: "David Kim",
      authorAvatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=david",
    },
    {
      id: "2",
      type: "video",
      title: "Building Responsive Layouts",
      description: "A comprehensive guide to creating responsive web layouts.",
      thumbnail:
        "https://images.unsplash.com/photo-1558655146-d09347e92766?w=800&q=80",
      author: "Emma Wilson",
      authorAvatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=emma",
    },
  ],
  courses = [
    {
      id: "1",
      title: "JavaScript Fundamentals",
      description: "Learn the basics of JavaScript programming language.",
      instructor: "Robert Smith",
      instructorAvatar:
        "https://api.dicebear.com/7.x/avataaars/svg?seed=robert",
      progress: 75,
      enrolled: true,
      thumbnail:
        "https://images.unsplash.com/photo-1579468118864-1b9ea3c0db4a?w=800&q=80",
    },
    {
      id: "2",
      title: "Advanced React Patterns",
      description: "Master advanced React patterns and techniques.",
      instructor: "Jennifer Lee",
      instructorAvatar:
        "https://api.dicebear.com/7.x/avataaars/svg?seed=jennifer",
      progress: 30,
      enrolled: true,
      thumbnail:
        "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=800&q=80",
    },
    {
      id: "3",
      title: "UI/UX Design Principles",
      description: "Learn the fundamental principles of UI/UX design.",
      instructor: "Carlos Mendez",
      instructorAvatar:
        "https://api.dicebear.com/7.x/avataaars/svg?seed=carlos",
      price: "$49.99",
      enrolled: false,
      thumbnail:
        "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=800&q=80",
    },
  ],
}) => {
  const [activeTab, setActiveTab] = useState("overview");
  const isRtl = language === "ar";

  return (
    <div
      className={`w-full min-h-screen bg-background ${isRtl ? "rtl" : "ltr"}`}
    >
      <div className="container mx-auto p-4 md:p-6">
        <Button variant="outline" size="sm" className="mb-4">
          <ChevronLeft className="h-4 w-4 mr-2" />
          {isRtl ? "العودة" : "Back"}
        </Button>

        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold">
              {isRtl ? "لوحة التحكم" : "Dashboard"}
            </h1>
            <p className="text-muted-foreground">
              {isRtl ? `مرحباً، ${userName}` : `Welcome back, ${userName}`}
            </p>
          </div>

          <div className="flex items-center space-x-4 mt-4 md:mt-0">
            <Button variant="outline" size="sm">
              <Bell className="h-4 w-4 mr-2" />
              {isRtl ? "الإشعارات" : "Notifications"}
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              {isRtl ? "الإعدادات" : "Settings"}
            </Button>
            <div className="flex items-center space-x-2">
              <Avatar>
                <AvatarImage
                  src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${userName}`}
                  alt={userName}
                />
                <AvatarFallback>
                  {userName.substring(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
            </div>
          </div>
        </div>

        {/* Main Navigation Tabs */}
        <Tabs
          defaultValue="overview"
          className="mb-8"
          onValueChange={setActiveTab}
        >
          <TabsList className="mb-6">
            <TabsTrigger value="overview">
              <Globe className="h-4 w-4 mr-2" />
              {isRtl ? "نظرة عامة" : "Overview"}
            </TabsTrigger>
            <TabsTrigger value="communities">
              <Users className="h-4 w-4 mr-2" />
              {isRtl ? "المجتمعات" : "Communities"}
            </TabsTrigger>
            <TabsTrigger value="courses">
              <BookOpen className="h-4 w-4 mr-2" />
              {isRtl ? "الدورات" : "Courses"}
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab Content */}
          <TabsContent value="overview" className="space-y-6">
            {/* Communities Section */}
            <section>
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">
                  {isRtl ? "مجتمعاتك" : "Your Communities"}
                </h2>
                <Button variant="ghost" size="sm">
                  {isRtl ? "عرض الكل" : "View All"}
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {communities.slice(0, 3).map((community) => (
                  <CommunityCard
                    key={community.id}
                    name={community.name}
                    description={community.description}
                    memberCount={community.memberCount}
                    thumbnail={community.thumbnail}
                  />
                ))}
              </div>
            </section>

            {/* Recent Activity and Featured Content */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Recent Activity */}
              <Card className="lg:col-span-1">
                <CardHeader>
                  <CardTitle>
                    {isRtl ? "النشاط الأخير" : "Recent Activity"}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recentActivity.map((activity) => (
                      <div
                        key={activity.id}
                        className="flex items-start space-x-4 p-2 rounded-md hover:bg-muted transition-colors"
                      >
                        <Avatar className="h-8 w-8">
                          <AvatarImage
                            src={activity.authorAvatar}
                            alt={activity.author}
                          />
                          <AvatarFallback>
                            {activity.author.substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <p className="text-sm font-medium">
                            {activity.title}
                          </p>
                          <div className="flex items-center text-xs text-muted-foreground">
                            <span>{activity.author}</span>
                            <span className="mx-1">•</span>
                            <span>{activity.timestamp}</span>
                          </div>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {activity.type}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Featured Content */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle>
                    {isRtl ? "المحتوى المميز" : "Featured Content"}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ContentFeed content={featuredContent} />
                </CardContent>
              </Card>
            </div>

            {/* Courses Section */}
            <section>
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">
                  {isRtl ? "دوراتك" : "Your Courses"}
                </h2>
                <Button variant="ghost" size="sm">
                  {isRtl ? "عرض الكل" : "View All"}
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {courses.slice(0, 3).map((course) => (
                  <CourseCard
                    key={course.id}
                    title={course.title}
                    description={course.description}
                    instructor={course.instructor}
                    instructorAvatar={course.instructorAvatar}
                    progress={course.progress}
                    enrolled={course.enrolled}
                    price={course.price}
                    thumbnail={course.thumbnail}
                  />
                ))}
              </div>
            </section>
          </TabsContent>

          {/* Communities Tab Content */}
          <TabsContent value="communities" className="space-y-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">
                {isRtl ? "المجتمعات" : "Communities"}
              </h2>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                {isRtl ? "إنشاء مجتمع" : "Create Community"}
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {communities.map((community) => (
                <CommunityCard
                  key={community.id}
                  name={community.name}
                  description={community.description}
                  memberCount={community.memberCount}
                  thumbnail={community.thumbnail}
                />
              ))}
            </div>
          </TabsContent>

          {/* Courses Tab Content */}
          <TabsContent value="courses" className="space-y-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">
                {isRtl ? "الدورات" : "Courses"}
              </h2>
              <div className="flex space-x-2">
                <Button variant="outline">{isRtl ? "تصفية" : "Filter"}</Button>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  {isRtl ? "إنشاء دورة" : "Create Course"}
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {courses.map((course) => (
                <CourseCard
                  key={course.id}
                  title={course.title}
                  description={course.description}
                  instructor={course.instructor}
                  instructorAvatar={course.instructorAvatar}
                  progress={course.progress}
                  enrolled={course.enrolled}
                  price={course.price}
                  thumbnail={course.thumbnail}
                />
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default CommunityDashboard;
