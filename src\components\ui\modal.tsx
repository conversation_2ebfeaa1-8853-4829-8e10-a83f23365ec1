"use client"

import { Dialog } from "@chakra-ui/react"
import { forwardRef } from "react"

export interface ModalProps extends Omit<Dialog.RootProps, 'open' | 'onOpenChange'> {
  isOpen?: boolean
  onClose?: () => void
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export const Modal = forwardRef<HTMLDivElement, ModalProps>(
  function Modal(props, ref) {
    const { isOpen, onClose, open, onOpenChange, ...rest } = props

    // Support both old and new API
    const isModalOpen = open !== undefined ? open : isOpen
    const handleOpenChange = onOpenChange || ((open: boolean) => {
      if (!open && onClose) {
        onClose()
      }
    })

    return (
      <Dialog.Root
        ref={ref}
        open={isModalOpen}
        onOpenChange={handleOpenChange}
        {...rest}
      />
    )
  },
)

export interface ModalOverlayProps extends Dialog.BackdropProps {}

export const ModalOverlay = forwardRef<HTMLDivElement, ModalOverlayProps>(
  function ModalOverlay(props, ref) {
    return <Dialog.Backdrop ref={ref} {...props} />
  },
)

export interface ModalContentProps extends Dialog.ContentProps {}

export const ModalContent = forwardRef<HTMLDivElement, ModalContentProps>(
  function ModalContent(props, ref) {
    return <Dialog.Content ref={ref} {...props} />
  },
)

export interface ModalHeaderProps extends Dialog.HeaderProps {}

export const ModalHeader = forwardRef<HTMLDivElement, ModalHeaderProps>(
  function ModalHeader(props, ref) {
    return <Dialog.Header ref={ref} {...props} />
  },
)

export interface ModalCloseButtonProps extends Dialog.CloseTriggerProps {}

export const ModalCloseButton = forwardRef<HTMLButtonElement, ModalCloseButtonProps>(
  function ModalCloseButton(props, ref) {
    return <Dialog.CloseTrigger ref={ref} {...props} />
  },
)

export interface ModalBodyProps extends Dialog.BodyProps {}

export const ModalBody = forwardRef<HTMLDivElement, ModalBodyProps>(
  function ModalBody(props, ref) {
    return <Dialog.Body ref={ref} {...props} />
  },
)

export interface ModalFooterProps extends Dialog.FooterProps {}

export const ModalFooter = forwardRef<HTMLDivElement, ModalFooterProps>(
  function ModalFooter(props, ref) {
    return <Dialog.Footer ref={ref} {...props} />
  },
)
