import { Box, BoxProps } from "@chakra-ui/react";
import { forwardRef } from "react";

export interface CardProps extends BoxProps {}

export const Card = forwardRef<HTMLDivElement, CardProps>((props, ref) => {
  return (
    <Box
      ref={ref}
      borderWidth="1px"
      borderRadius="lg"
      bg="white"
      _dark={{ bg: "gray.800" }}
      shadow="sm"
      {...props}
    />
  );
});

Card.displayName = "Card";

export interface CardBodyProps extends BoxProps {}

export const CardBody = forwardRef<HTMLDivElement, CardBodyProps>((props, ref) => {
  return <Box ref={ref} p={6} {...props} />;
});

CardBody.displayName = "CardBody";
