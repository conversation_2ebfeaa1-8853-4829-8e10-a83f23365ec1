import { cn } from './utils';

describe('cn', () => {
  it('should merge class names correctly', () => {
    expect(cn('bg-red-500', 'text-white')).toBe('bg-red-500 text-white');
  });

  it('should handle conditional classes correctly with clsx behavior', () => {
    expect(cn('base', true && 'conditional-true', false && 'conditional-false')).toBe('base conditional-true');
  });

  it('should override conflicting classes with tailwind-merge behavior', () => {
    expect(cn('p-2', 'p-4')).toBe('p-4');
    expect(cn('p-4', 'p-2')).toBe('p-2');
    expect(cn('px-2', 'p-4')).toBe('p-4');
    expect(cn('p-4', 'px-2')).toBe('p-4 px-2');
  });

  it('should handle mixed conditional and string classes', () => {
    expect(cn('p-2', { 'bg-blue-500': true, 'text-yellow-300': false }, 'mx-4')).toBe('p-2 bg-blue-500 mx-4');
  });

  it('should return empty string for no inputs', () => {
    expect(cn()).toBe('');
  });

  it('should ignore null, undefined, and false values', () => {
    expect(cn('text-lg', null, undefined, false, 'font-bold')).toBe('text-lg font-bold');
  });
});
