"use client";

import { useState } from "react";
import {
  Menu as MenuIcon,
  X,
  ChevronDown,
  User,
  LogOut,
  Settings,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { useDirection } from "@/lib/contexts/DirectionContext";
import LanguageSwitcher from "@/components/common/LanguageSwitcher";
import { useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase/client";

export default function Header({ isAuthenticated = false }) {
  const [isOpen, setIsOpen] = useState(false);
  const { t } = useTranslation();
  const { direction } = useDirection();
  const router = useRouter();

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push("/auth/signin");
  };

  return (
    <div dir={direction}>
      <nav className="bg-white border-b border-gray-200 min-h-[60px] px-4 py-2">
        <div className="flex items-center justify-between">
          {/* Mobile menu button */}
          <div className="flex md:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="p-2 text-gray-600 hover:text-gray-800"
              aria-label="Toggle Navigation"
            >
              {isOpen ? (
                <X className="w-5 h-5" />
              ) : (
                <MenuIcon className="w-5 h-5" />
              )}
            </button>
          </div>

          {/* Logo and Desktop Nav */}
          <div className="flex items-center flex-1">
            <h1
              className="text-xl font-bold text-gray-800 cursor-pointer text-center md:text-left"
              onClick={() => router.push("/")}
            >
              Wuilt
            </h1>

            {/* Desktop Navigation */}
            <div className="hidden md:flex ml-10 space-x-4">
              <a href="/dashboard" className="text-gray-600 hover:text-gray-800 px-2 py-1">
                {t("common:nav.dashboard", "Dashboard")}
              </a>
              <a href="/communities" className="text-gray-600 hover:text-gray-800 px-2 py-1">
                {t("common:nav.communities", "Communities")}
              </a>
              <a href="/spaces" className="text-gray-600 hover:text-gray-800 px-2 py-1">
                {t("common:nav.spaces", "Spaces")}
              </a>
              <a href="/settings" className="text-gray-600 hover:text-gray-800 px-2 py-1">
                {t("common:nav.settings", "Settings")}
              </a>
            </div>
          </div>

          {/* Right side actions */}
          <div className="flex items-center space-x-4">
            <LanguageSwitcher />

            {isAuthenticated ? (
              <div className="relative">
                <button className="flex items-center space-x-2 text-gray-600 hover:text-gray-800">
                  <img
                    className="w-8 h-8 rounded-full"
                    src="https://api.dicebear.com/7.x/avataaars/svg?seed=John"
                    alt="Profile"
                  />
                </button>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <a
                  href="/login"
                  className="text-gray-600 hover:text-gray-800 px-3 py-1"
                >
                  {t("common:nav.signIn", "Sign In")}
                </a>
                <a
                  href="/signup"
                  className="hidden md:inline-flex bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 transition-colors"
                >
                  {t("common:nav.signUp", "Sign Up")}
                </a>
              </div>
            )}
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden mt-4 pb-4 border-t border-gray-200 pt-4">
            <div className="space-y-2">
              <a href="/dashboard" className="block text-gray-600 hover:text-gray-800 px-2 py-1">
                {t("common:nav.dashboard", "Dashboard")}
              </a>
              <a href="/communities" className="block text-gray-600 hover:text-gray-800 px-2 py-1">
                {t("common:nav.communities", "Communities")}
              </a>
              <a href="/spaces" className="block text-gray-600 hover:text-gray-800 px-2 py-1">
                {t("common:nav.spaces", "Spaces")}
              </a>
              <a href="/settings" className="block text-gray-600 hover:text-gray-800 px-2 py-1">
                {t("common:nav.settings", "Settings")}
              </a>
              {!isAuthenticated && (
                <a href="/auth/signup" className="block bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 transition-colors mt-4">
                  {t("common:nav.signUp", "Sign Up")}
                </a>
              )}
            </div>
          </div>
        )}
      </nav>
    </div>
  );
}


