"use client"

import { useState, useCallback } from "react"

export interface UseDisclosureReturn {
  isOpen: boolean
  onOpen: () => void
  onClose: () => void
  onToggle: () => void
}

export interface UseDisclosureProps {
  defaultIsOpen?: boolean
  onOpen?: () => void
  onClose?: () => void
}

export function useDisclosure(props: UseDisclosureProps = {}): UseDisclosureReturn {
  const { defaultIsOpen = false, onOpen: onOpenProp, onClose: onCloseProp } = props
  
  const [isOpen, setIsOpen] = useState(defaultIsOpen)

  const onOpen = useCallback(() => {
    setIsOpen(true)
    onOpenProp?.()
  }, [onOpenProp])

  const onClose = useCallback(() => {
    setIsOpen(false)
    onCloseProp?.()
  }, [onCloseProp])

  const onToggle = useCallback(() => {
    if (isOpen) {
      onClose()
    } else {
      onOpen()
    }
  }, [isOpen, onOpen, onClose])

  return {
    isOpen,
    onOpen,
    onClose,
    onToggle,
  }
}
