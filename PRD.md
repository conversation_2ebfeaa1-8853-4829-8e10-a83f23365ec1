scenius.community
A multilingual SaaS Community & LMS Builder Powered By Wuilt.com

Project Core Features:
1. Community Management System
- Community Creation & Management: Users can create and manage communities with customizable settings.
- Spaces & Collections: Communities can be organized into spaces and collections for better content organization.
- Multilingual Support: Built-in language switching between English and Arabic.
- Role-Based Access Control: Different roles (admin, moderator, member) with specific permissions.

3. Content Management
- Dynamic Content Types: Support for various content formats (posts, articles, discussions).
- Customizable templates and layout options for each content type
- Content Reactions & Comments: Users can engage with content through reactions and comments.
- Content Filtering & Sorting: Advanced content filtering with search capabilities.
- Pin/Featured Content: Important content can be pinned within spaces.
- E-learning & Courses Features:(Space Type Course)
- Course structure with modules and lessons
Progress Tracking
Quizzes with Auto-Grading
Assignment and assessment tools
Certificates and achievements
Instructor Tools
Course analytics
Student management 

4. User Management
- Authentication: Email/password authentication system with signup/signin flows.
- User Profiles: Customizable user profiles with bio, expertise, and preferences.
- Member Management: Tools for community administrators to manage members.
- Community Signup Flow: community administrators can setup customized signup flows
- Join Requests: Support for private community access through join requests.
- Role-Based Access Control: Custom role creation capabilities & permission management system 
- User Roles: (Owner, Admin, Moderator, Member, Guest)

5. Messaging and Communication
- Direct Messaging (1:1 and group conversations)
- Media sharing
- Message organization
- Search functionality
- Notification Center
- Customizable preferences
- Multi-channel delivery
- Activity digests
- Real-time alerts

6. Monetization Features
- Paid Courses
- Paid Membership Subscriptions
- AdSense integration
- Premium Listings In Directories
- Gated and Premium content
- Digital Products
- Sponsored content & Banners

7. Analytics & Reporting
- Content Analytics: Track engagement metrics like views, comments, and reactions.
- Community Growth: Monitor community member growth and activity.
- Engagement Metrics: Dashboard for visualizing user engagement data.
- Web Analytics

8. Additional Features
- Communities Templates Hub
- Gamifications, Badges, & Leaderboards
- Live streaming 
- Notification System 
- Feeds Stream
- Moderation Tools
- Courses & Certifications Management Tools 
- Affiliate Marketing System
- Integrations & App Store

9. Social Commerce (reference: social.plus/monetization)
- Social Shopping (integrating ecommerce into social interactions) 
- Chat commerce (purchases directly through messaging)
- Live commerce ( live streaming with instant purchasing options)

* Platform Structure, Templates, & Customization
Community > Collections > Spaces > Modules

* Templates:
- Theme Customization
- Custom color schemes
- Font selection options
- Light/dark mode support
- Visual elements customization

* Communities Types Templates (Main Use Cases)
- Forums & Discussion
- Social Community
- Customers Platform
- E-Learning, Academy, & Courses
- Company Intranet
- Help Center & Knowledge Base
- Directories & Listings
- Content Creators & Creatives Hub
- Social Commerce

Communities:
- The main entity that customers creates, signups, or request to join, according to community settings
- Created under user accounts (Community Owner)
- Published on subdomains, ability to connect custom domain 
- Multiple types, template-based approach
- Visibility & Access Options
- Customizable navigation, layout, & branding
- Dynamic content type support

Spaces:
- The entity that contain the content
- Created under a community, could be placed into a collection
- Published on community subdomains, with a unique URL, 
- Multiple types, template-based approach
- Access & Visibility Options
- Customizable navigation and layout
- Dynamic content type support
- The Basic Types:
Forums    (Traditional thread-based discussion space)
Posts     (Posts by community members or admins)
Feeds     (Aggregated updates from multiple sources or spaces)
Courses   (Learning modules with lessons, quizzes, certificates)
Blogs     (Editorial content with categories and tags)
Help Centers (Knowledgebase articles and guides)
Ticketing    (Support request submission and tracking)
Product Updates (nnouncements and changelogs)
Product Roadmap  (Visual representation of upcoming features)
Feature Requests   (Crowdsourced feedback with voting)
Resource Libraries  (Curated documents, PDFs, videos, tools, and files)
Directories   (Listings of members, businesses, or tools with filters and maps)
Digital Products   (Sellable assets like, eBooks, templates, downloads)
Events  (Event listing and RSVP functionality)
Bookings  (Scheduled meetings, appointments, Services, or sessions)
Chats  (Real-time 1:1 or group messaging)
Live-streams  (Embedded or native video streaming, Zoom, YouTube Live integrations)
Groups (community members, grouped together by the admin or common characteristics) 
Questions  (Stack Overflow-style Q&A module with up-voting)
Changelog  (Dedicated log of platform or community updates)


* Content Types:
- Documents (Articles, Blog Posts, Lessons, Help Center Articles, ect.)   
- Post types (Discussion, Question, Announcement, Requests, etc.)
- Voting & Polls (Requested Features, Surveys, Feedback, etc.) 
- Listings (Employees Listings, Local Directories) 
- Comments (Multi threaded comments and replies)
- Reactions (Thumbs & Emojis) 
- Embeds (Maps - Reviews - Videos - etc.)  
- E-learning & Courses  (Structured Modules, Progress Tracking, Deadlines, Certificates)



* Subdomain Publishing feature for Free Trial Communities
- Subdomain Structure & Management:
Format: {community-name}.scenius.community
Auto-generated subdomain suggestions based on community name
- Validation rules:
Lowercase letters, numbers, and hyphens only
No special characters or spaces
Length limits (e.g., 3-63 characters)
Reserved subdomains protection (admin, api, www, etc.)

- Free Trial Features:
Automatic subdomain provisioning upon community creation
Custom subdomain availability checker
Temporary subdomain during trial period
Clear upgrade paths for custom domain usage
Trial limitations clearly communicated to users

* DNS & Routing:
- Wildcard SSL certificate for all subdomains
- Automatic SSL configuration
- Dynamic routing based on subdomain detection
- Community content resolution based on subdomain

* Software Architecture, Tech Stack & Database Design
- Overall Architecture Approach
a monolithic architecture with service-oriented design (Modular monolith)
The architecture will be structured as a single application with clear internal module boundaries that mirror potential future microservices. 
This approach provides a balance between development speed and maintainability. 

* Core Services:

- Authentication & Authorization Service
User registration and	login 
JWT token management
Role-based access control 
OAuth	integration (for future phases)

- Community Service
Community creation and configuration 
Community membership management 
Community discovery	and search 
Template application and customization

- Spaces Service
Space	creation and management
Content organization within spaces 
Space-specific	settings and permissions

- Users Service
User profile management
User roles and permissions
User activity tracking
Notification preferences

- Localization Service
Language detection and switching
Translation management
RTL/LTR layout handling
Locale-specific formatting (dates, numbers, etc.)

- Shared Infrastructure: 
Logging and monitoring 
Configuration management 
Error handling 
API gateway for future expansion

* Inheritance & Key Concepts
Multi Tenancy
Modular Inheritance: Default configs flow top-down (Community → Space → Module), but can be overridden at lower levels.
Configurable Schema: Store settings per level (e.g. theme, visibility, permissions).
Types & Templates: Use reusable blueprints for community/space/module types.

* Internationalization Strategy
1. Resource Bundle Approach:
All UI text will be stored in language-specific resource files
Backend error messages and notifications will use message keys
Dynamic content will be stored with language indicators
2. Language Detection and Selection:
Automatic detection based on browser/device settings
User preference override stored in profile
Language selector prominently available in UI
Language preference stored in user session
3. Translation Management:
Static content translated during development
User-generated content can be stored in multiple languages
Translation table for community/space metadata

RTL/LTR Support:

1. CSS and Layout Strategy:
Use of CSS logical properties (start/end instead of left/right)
CSS variables for direction-dependent values
Flexbox and Grid layouts with direction awareness
Bidirectional (bidi) CSS implementation

2. Component Design:
Direction-agnostic component library
Mirroring of UI elements when switching directions
Special handling for numbers, dates, and currency

3. Testing and QA:
Dedicated test cases for RTL layouts
Visual regression testing for both directions
Bilingual testers for natural language validation

* Tech Stack:

- Frontend 

Framework: Next.js (React-based)
Server-side rendering for improved SEO
Built-in	routing	and API routes
TypeScript for type safety

UI Components:
Chakra UI (supports	RTL out of the box)
Custom theme with bilingual design tokens

State Management: 
React Query
React Context API

Internationalization:
React-i18next with next-i18next integration 
RTL-LTR switching via CSS variables and logical properties

- Backend:
Framework: NestJS (Node.js) 
TypeScript support 
Modular architecture that maps well to our service boundaries 
Built-in dependency injection 
Strong validation and documentation capabilities

- Database: Supabase PostgreSQL
- Authentication: Supabase Auth
- Deployment: Vercel (recommended for Next.js)
- Edge Functions: Supabase Edge Functions
- Image Optimization: Next.js Image component
- Performance Monitoring: Vercel Analytics
- Content Management: Dynamic content types system


* Development and Deployment Strategy
 Development Workflow
 1. Code Organization:
Modular monolith with clear boundaries between services
Shared core utilities and interfaces
Service-specific folders with consistent structure
 2. Testing Strategy:
Unit tests for business logic
Integration tests for API endpoints
E2E tests for critical user journeys
Visual regression tests for RTL/LTR layouts
 3. Code Quality:
ESLint and Prettier for code formatting
Husky for pre-commit hooks
SonarQube for code quality analysis
Pull request reviews with at least one approver
 
Deployment Pipeline
 1. Environments:
Development (continuous deployment from main branch)
Staging (manual promotion from development)
Production (manual promotion from staging)
 2. Deployment Process:
Build and test in CI pipeline
Create versioned Docker image
Deploy to target environment
Run database migrations
Perform smoke tests
Enable monitoring and alerts
 3. Database Management:
Version-controlled migrations
Backup strategy (daily backups, point-in-time recovery)
Seed data for testing environments
 4. Monitoring and Operations:
Application performance monitoring
Error tracking and alerting
Usage analytics
Security scanning

* Architectural Patterns:
Server Components for static content
Client Components for interactive features
Edge middleware for routing & authentication
API Routes for server-side operations
ISR (Incremental Static Regeneration) for community pages
On-demand revalidation for dynamic content

Rendering Modes
ISR >>>  Blog, FAQs, marketing pages >>> getStaticProps() + revalidate
SSR >>> User dashboard, profile >>> getServerSideProps()
SWR  >>> Real-time updates (notifications, views) >>> useSWR()
Middleware >>>  Auth redirect, A/B testing >>> middleware.ts 

* Database Design
Logical Schemas: one schema per domain “Space” (e.g. courses, directory, blog) to isolate migrations and permissions

- Core Tables:
profiles: User profile information
communities: Community settings and metadata
spaces:  Space-specific settings and	permissions
collections: Groups of related spaces
user_roles: Role assignments for users

- Relationship Tables:
community_members: Users belonging to communities
space_members: Users belonging to spaces
content_reactions: User reactions to content
content_comments: User comments on content items
content_views: Tracking content views

- Auxiliary Tables:
badges: Custom badges for community members
profile_fields: Custom profile fields configuration
community_join_requests: Requests to join private communities
notifications: User notifications


* References

- Chakra UI
https://chakra-ui.com/

- Supabase Documentation
https://supabase.com/docs/guides/auth/server-side

- Next.js Documentation
https://nextjs.org/docs/pages/getting-started/installation

- Vercel Documentation
https://vercel.com/docs

