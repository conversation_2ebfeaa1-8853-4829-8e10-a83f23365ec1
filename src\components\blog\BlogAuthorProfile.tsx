"use client";

import {
  Box,
  Container,
  Heading,
  Text,
  Flex,
  SimpleGrid,
  Link,
  Badge,
  Button,
  HStack,
  Icon,
} from "@chakra-ui/react";
import { Avatar } from "@/components/ui/avatar";
import { useColorModeValue } from "@/components/ui/color-mode";
import { Card, CardBody } from "@/components/ui/card";
import { Divider } from "@/components/ui/separator";
import { StatRoot, StatLabel, StatValueText } from "@/components/ui/stat";
import { useTranslation } from "react-i18next";
import NextLink from "next/link";
import { formatDistanceToNow } from "date-fns";
import { useDirection } from "@/lib/contexts/DirectionContext";
import {
  MessageSquare,
  ThumbsUp,
  Globe,
  Mail,
  Twitter,
  Linkedin,
  Github,
} from "lucide-react";

interface BlogAuthorProfileProps {
  author: any;
  posts: any[];
  stats?: {
    totalPosts: number;
    totalLikes: number;
    totalComments: number;
  };
}

export default function BlogAuthorProfile({
  author,
  posts,
  stats = { totalPosts: 0, totalLikes: 0, totalComments: 0 },
}: BlogAuthorProfileProps) {
  const { t } = useTranslation();
  const { direction } = useDirection();

  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  // Calculate stats if not provided
  if (!stats.totalPosts) stats.totalPosts = posts.length;
  if (!stats.totalLikes) {
    stats.totalLikes = posts.reduce((total, post) => {
      return total + (post.reactions?.[0]?.count || 0);
    }, 0);
  }
  if (!stats.totalComments) {
    stats.totalComments = posts.reduce((total, post) => {
      return total + (post.comments?.[0]?.count || 0);
    }, 0);
  }

  // Social links from author metadata
  const socialLinks = author?.metadata?.socialLinks || {};

  return (
    <Box dir={direction}>
      <Container maxW="container.xl" py={6}>
        {/* Author Profile Header */}
        <Box
          bg={bgColor}
          borderRadius="xl"
          borderWidth="1px"
          borderColor={borderColor}
          p={8}
          mb={8}
        >
          <Flex
            direction={{ base: "column", md: "row" }}
            align={{ base: "center", md: "flex-start" }}
            gap={8}
          >
            <Avatar
              size="2xl"
              name={author?.full_name}
              src={author?.avatar_url}
            />
            <Box flex={1}>
              <Heading as="h1" size="xl" mb={2}>
                {author?.full_name}
              </Heading>
              <Text color="gray.600" fontSize="lg" mb={4}>
                {author?.username ? `@${author.username}` : ""}
              </Text>
              <Text mb={6}>
                {author?.bio ||
                  t("blog.noBio", "This author has not added a bio yet.")}
              </Text>

              {/* Social Links */}
              {Object.keys(socialLinks).length > 0 && (
                <HStack gap={4} mb={6}>
                  {socialLinks.website && (
                    <Link href={socialLinks.website} target="_blank" rel="noopener noreferrer">
                      <Icon as={Globe} boxSize={5} />
                    </Link>
                  )}
                  {socialLinks.email && (
                    <Link href={`mailto:${socialLinks.email}`}>
                      <Icon as={Mail} boxSize={5} />
                    </Link>
                  )}
                  {socialLinks.twitter && (
                    <Link href={socialLinks.twitter} target="_blank" rel="noopener noreferrer">
                      <Icon as={Twitter} boxSize={5} />
                    </Link>
                  )}
                  {socialLinks.linkedin && (
                    <Link href={socialLinks.linkedin} target="_blank" rel="noopener noreferrer">
                      <Icon as={Linkedin} boxSize={5} />
                    </Link>
                  )}
                  {socialLinks.github && (
                    <Link href={socialLinks.github} target="_blank" rel="noopener noreferrer">
                      <Icon as={Github} boxSize={5} />
                    </Link>
                  )}
                </HStack>
              )}

              {/* Stats */}
              <SimpleGrid columns={{ base: 1, sm: 3 }} gap={4}>
                <StatRoot>
                  <StatLabel>{t("blog.posts", "Posts")}</StatLabel>
                  <StatValueText value={stats.totalPosts} />
                </StatRoot>
                <StatRoot>
                  <StatLabel>{t("blog.likes", "Likes")}</StatLabel>
                  <StatValueText value={stats.totalLikes} />
                </StatRoot>
                <StatRoot>
                  <StatLabel>{t("blog.comments", "Comments")}</StatLabel>
                  <StatValueText value={stats.totalComments} />
                </StatRoot>
              </SimpleGrid>
            </Box>
          </Flex>
        </Box>

        {/* Author's Posts */}
        <Heading as="h2" size="lg" mb={6}>
          {t("blog.authorPosts", "Posts by {{name}}", {
            name: author?.full_name,
          })}
        </Heading>

        {posts.length > 0 ? (
          <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} gap={6}>
            {posts.map((post) => (
              <Card key={post.id} overflow="hidden">
                {post.content?.image && (
                  <Box height="200px" overflow="hidden">
                    <img
                      src={post.content.image}
                      alt={post.title}
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                      }}
                    />
                  </Box>
                )}
                <CardBody>
                  {post.metadata?.category && (
                    <Badge colorScheme="teal" mb={2}>
                      {post.metadata.category}
                    </Badge>
                  )}
                  <Heading as="h3" size="md" mb={2}>
                    <Link
                      as={NextLink}
                      href={`/spaces/${post.space_id}/posts/${post.id}`}
                      _hover={{ textDecoration: "underline" }}
                    >
                      {post.title}
                    </Link>
                  </Heading>
                  <Text color="gray.500" fontSize="sm" mb={3}>
                    {formatDistanceToNow(new Date(post.created_at), {
                      addSuffix: true,
                    })}
                  </Text>
                  <Text lineClamp={3} mb={4}>
                    {typeof post.content === "string"
                      ? post.content
                      : post.content.text || ""}
                  </Text>

                  <Box mb={4}>
                    <Divider />
                  </Box>

                  <Flex justify="space-between">
                    <Flex align="center" gap={1}>
                      <ThumbsUp size={16} />
                      <Text fontSize="sm">
                        {post.reactions?.[0]?.count || 0}
                      </Text>
                    </Flex>
                    <Flex align="center" gap={1}>
                      <MessageSquare size={16} />
                      <Text fontSize="sm">
                        {post.comments?.[0]?.count || 0}
                      </Text>
                    </Flex>
                  </Flex>
                </CardBody>
              </Card>
            ))}
          </SimpleGrid>
        ) : (
          <Box
            textAlign="center"
            py={10}
            borderWidth="1px"
            borderRadius="lg"
            borderColor={borderColor}
          >
            <Text>
              {t("blog.noPosts", "This author hasn't published any posts yet.")}
            </Text>
          </Box>
        )}
      </Container>
    </Box>
  );
}
