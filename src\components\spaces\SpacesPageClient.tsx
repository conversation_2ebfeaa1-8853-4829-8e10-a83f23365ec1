"use client";

import { Box, Heading, Text, Flex, Button } from "@chakra-ui/react";
import { Plus } from "lucide-react";
import { useDirection } from "@/lib/contexts/DirectionContext";
import SpacesGrid from "@/components/spaces/SpacesGrid";

interface SpacesPageClientProps {
  spaces: any[];
}

export default function SpacesPageClient({ spaces }: SpacesPageClientProps) {
  const { direction } = useDirection();

  // Temporarily bypass translation issues
  const getText = (key: string, fallback: string) => {
    return fallback;
  };

  return (
    <Box dir={direction}>
      <Flex justify="space-between" align="center" mb={6}>
        <Box>
          <Heading as="h1" size="xl" mb={2}>
            {getText("spaces.title", "Spaces")}
          </Heading>
          <Text color="gray.600">
            {getText("spaces.description", "Manage and explore your community spaces")}
          </Text>
        </Box>
        <Button colorPalette="teal" leftIcon={<Plus size={16} />}>
          {getText("spaces.create", "Create Space")}
        </Button>
      </Flex>

      <SpacesGrid spaces={spaces} />
    </Box>
  );
}
