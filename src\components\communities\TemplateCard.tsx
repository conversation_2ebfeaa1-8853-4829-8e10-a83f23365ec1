import React from "react";
import {
  Box,
  Card,
  CardBody,
  Heading,
  Text,
  Image,
  Stack,
  Badge,
  Button,
  HStack,
  Flex,
  useColorModeValue,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";

interface TemplateCardProps {
  id: string;
  name: string;
  description: string;
  thumbnailUrl: string;
  category: string;
  tags: string[];
}

const TemplateCard = ({
  id,
  name,
  description,
  thumbnailUrl,
  category,
  tags = [],
}: TemplateCardProps) => {
  const { t } = useTranslation();
  const router = useRouter();

  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  const handleUseTemplate = () => {
    // In a real implementation, this would navigate to template setup
    router.push(`/templates/${id}/setup`);
  };

  const handlePreview = () => {
    // In a real implementation, this would open a preview
    router.push(`/templates/${id}/preview`);
  };

  return (
    <Card
      overflow="hidden"
      variant="outline"
      borderColor={borderColor}
      bg={bgColor}
      h="100%"
      transition="transform 0.2s"
      _hover={{ transform: "translateY(-4px)", boxShadow: "md" }}
    >
      <Box position="relative" height="180px" overflow="hidden">
        <Image
          src={thumbnailUrl}
          alt={name}
          objectFit="cover"
          width="100%"
          height="100%"
        />
        <Badge
          position="absolute"
          top={3}
          right={3}
          colorScheme="teal"
          fontSize="sm"
          px={2}
          py={1}
          borderRadius="md"
        >
          {category}
        </Badge>
      </Box>

      <CardBody>
        <Stack spacing={3}>
          <Heading size="md">{name}</Heading>
          <Text noOfLines={2} color="gray.600">
            {description}
          </Text>

          <HStack spacing={2} flexWrap="wrap">
            {tags.map((tag, index) => (
              <Badge key={index} colorScheme="gray" variant="subtle">
                {tag}
              </Badge>
            ))}
          </HStack>

          <Flex gap={2} mt={2}>
            <Button
              variant="solid"
              colorScheme="teal"
              size="sm"
              flex={1}
              onClick={handleUseTemplate}
            >
              {t("templates.useTemplate", "Use Template")}
            </Button>
            <Button
              variant="outline"
              colorScheme="teal"
              size="sm"
              flex={1}
              onClick={handlePreview}
            >
              {t("templates.preview", "Preview")}
            </Button>
          </Flex>
        </Stack>
      </CardBody>
    </Card>
  );
};

export default TemplateCard;
