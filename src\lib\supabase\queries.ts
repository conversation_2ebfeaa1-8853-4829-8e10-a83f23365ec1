import type { SupabaseClient } from '@supabase/supabase-js';

export interface UserProfile {
  id: string;
  created_at: string;
  updated_at: string;
  full_name: string | null;
  avatar_url: string | null;
  bio: string | null;
  website: string | null;
  language: string;
  username: string | null;
}

export async function getUserProfileById(
  supabase: SupabaseClient,
  userId: string
): Promise<{ data: UserProfile | null; error: any }> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    return { data, error };
  } catch (error) {
    throw error;
  }
}

export async function updateUserProfile(
  supabase: SupabaseClient,
  userId: string,
  updates: Partial<UserProfile>
): Promise<{ data: UserProfile | null; error: any }> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();

    return { data, error };
  } catch (error) {
    throw error;
  }
}

export async function createUserProfile(
  supabase: SupabaseClient,
  profile: Omit<UserProfile, 'created_at' | 'updated_at'>
): Promise<{ data: UserProfile | null; error: any }> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .insert(profile)
      .select()
      .single();

    return { data, error };
  } catch (error) {
    throw error;
  }
}
