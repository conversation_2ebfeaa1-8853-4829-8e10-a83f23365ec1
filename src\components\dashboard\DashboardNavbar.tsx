"use client";

import {
  Box,
  Flex,
  Text,
  IconButton,
  Avatar,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  MenuDivider,
  HStack,
  useColorMode,
  InputGroup,
  InputLeftElement,
  Input,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase/client";
import { useDirection } from "@/lib/contexts/DirectionContext";
import {
  Menu as MenuIcon,
  Search,
  Bell,
  User,
  LogOut,
  Settings,
  Moon,
  Sun,
} from "lucide-react";
import LanguageSwitcher from "@/components/auth/LanguageSwitcher";

interface DashboardNavbarProps {
  onOpen: () => void;
  user: any;
}

export default function DashboardNavbar({
  onOpen,
  user,
}: DashboardNavbarProps) {
  const { t } = useTranslation();
  const { colorMode, toggleColorMode } = useColorMode();
  const { direction } = useDirection();
  const router = useRouter();

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push("/login");
    router.refresh();
  };

  return (
    <Flex
      ml={{ base: 0, md: 0 }}
      px={{ base: 4, md: 4 }}
      height="20"
      alignItems="center"
      bg="white"
      borderBottomWidth="1px"
      borderBottomColor="gray.200"
      justifyContent={{ base: "space-between", md: "flex-end" }}
      dir={direction}
    >
      <IconButton
        display={{ base: "flex", md: "none" }}
        onClick={onOpen}
        variant="outline"
        aria-label="open menu"
        icon={<MenuIcon />}
      />

      <Text
        display={{ base: "flex", md: "none" }}
        fontSize="2xl"
        fontWeight="bold"
        color="teal.600"
      >
        Scenius
      </Text>

      <HStack spacing={{ base: 0, md: 4 }}>
        <InputGroup display={{ base: "none", md: "flex" }} width="300px">
          <InputLeftElement pointerEvents="none">
            <Search color="gray.300" />
          </InputLeftElement>
          <Input
            type="text"
            placeholder={t("common.search")}
            variant="filled"
          />
        </InputGroup>

        <LanguageSwitcher />

        <IconButton
          size="lg"
          variant="ghost"
          aria-label="Toggle color mode"
          icon={colorMode === "light" ? <Moon size={20} /> : <Sun size={20} />}
          onClick={toggleColorMode}
        />

        <IconButton
          size="lg"
          variant="ghost"
          aria-label="Notifications"
          icon={<Bell size={20} />}
        />

        <Flex alignItems={"center"}>
          <Menu>
            <MenuButton
              py={2}
              transition="all 0.3s"
              _focus={{ boxShadow: "none" }}
            >
              <HStack>
                <Avatar
                  size={"sm"}
                  src={
                    user?.avatar_url ||
                    `https://api.dicebear.com/7.x/avataaars/svg?seed=${user?.id}`
                  }
                />
                <Box display={{ base: "none", md: "flex" }}>
                  <Text fontSize="sm">{user?.full_name || "User"}</Text>
                </Box>
              </HStack>
            </MenuButton>
            <MenuList
              bg="white"
              borderColor="gray.200"
            >
              <MenuItem icon={<User size={16} />}>{t("nav.profile")}</MenuItem>
              <MenuItem
                icon={<Settings size={16} />}
                onClick={() => router.push("/settings")}
              >
                {t("nav.settings")}
              </MenuItem>
              <MenuDivider />
              <MenuItem icon={<LogOut size={16} />} onClick={handleSignOut}>
                {t("auth.signOut")}
              </MenuItem>
            </MenuList>
          </Menu>
        </Flex>
      </HStack>
    </Flex>
  );
}
