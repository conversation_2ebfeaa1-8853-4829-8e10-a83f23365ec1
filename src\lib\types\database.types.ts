export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          username: string | null;
          full_name: string | null;
          avatar_url: string | null;
          bio: string | null;
          website: string | null;
          language: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          username?: string | null;
          full_name?: string | null;
          avatar_url?: string | null;
          bio?: string | null;
          website?: string | null;
          language?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          username?: string | null;
          full_name?: string | null;
          avatar_url?: string | null;
          bio?: string | null;
          website?: string | null;
          language?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      communities: {
        Row: {
          id: string;
          name: string;
          slug: string;
          description: string | null;
          logo_url: string | null;
          banner_url: string | null;
          is_private: boolean;
          owner_id: string | null;
          default_language: string;
          supports_rtl: boolean;
          theme: Json | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          slug: string;
          description?: string | null;
          logo_url?: string | null;
          banner_url?: string | null;
          is_private?: boolean;
          owner_id?: string | null;
          default_language?: string;
          supports_rtl?: boolean;
          theme?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          slug?: string;
          description?: string | null;
          logo_url?: string | null;
          banner_url?: string | null;
          is_private?: boolean;
          owner_id?: string | null;
          default_language?: string;
          supports_rtl?: boolean;
          theme?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      collections: {
        Row: {
          id: string;
          community_id: string;
          name: string;
          slug: string;
          description: string | null;
          icon: string | null;
          order_index: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          community_id: string;
          name: string;
          slug: string;
          description?: string | null;
          icon?: string | null;
          order_index?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          community_id?: string;
          name?: string;
          slug?: string;
          description?: string | null;
          icon?: string | null;
          order_index?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      spaces: {
        Row: {
          id: string;
          community_id: string;
          collection_id: string | null;
          name: string;
          slug: string;
          description: string | null;
          icon: string | null;
          type: string;
          is_private: boolean;
          settings: Json | null;
          order_index: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          community_id: string;
          collection_id?: string | null;
          name: string;
          slug: string;
          description?: string | null;
          icon?: string | null;
          type: string;
          is_private?: boolean;
          settings?: Json | null;
          order_index?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          community_id?: string;
          collection_id?: string | null;
          name?: string;
          slug?: string;
          description?: string | null;
          icon?: string | null;
          type?: string;
          is_private?: boolean;
          settings?: Json | null;
          order_index?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      content_modules: {
        Row: {
          id: string;
          space_id: string;
          author_id: string | null;
          title: string;
          content: Json;
          type: string;
          language: string;
          status: string;
          metadata: Json | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          space_id: string;
          author_id?: string | null;
          title: string;
          content: Json;
          type: string;
          language?: string;
          status?: string;
          metadata?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          space_id?: string;
          author_id?: string | null;
          title?: string;
          content?: Json;
          type?: string;
          language?: string;
          status?: string;
          metadata?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      community_members: {
        Row: {
          id: string;
          community_id: string;
          profile_id: string;
          role: string;
          joined_at: string;
        };
        Insert: {
          id?: string;
          community_id: string;
          profile_id: string;
          role?: string;
          joined_at?: string;
        };
        Update: {
          id?: string;
          community_id?: string;
          profile_id?: string;
          role?: string;
          joined_at?: string;
        };
      };
      space_members: {
        Row: {
          id: string;
          space_id: string;
          profile_id: string;
          role: string;
          joined_at: string;
        };
        Insert: {
          id?: string;
          space_id: string;
          profile_id: string;
          role?: string;
          joined_at?: string;
        };
        Update: {
          id?: string;
          space_id?: string;
          profile_id?: string;
          role?: string;
          joined_at?: string;
        };
      };
      content_reactions: {
        Row: {
          id: string;
          content_id: string;
          profile_id: string;
          reaction_type: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          content_id: string;
          profile_id: string;
          reaction_type: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          content_id?: string;
          profile_id?: string;
          reaction_type?: string;
          created_at?: string;
        };
      };
      content_comments: {
        Row: {
          id: string;
          content_id: string;
          author_id: string | null;
          parent_id: string | null;
          comment_text: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          content_id: string;
          author_id?: string | null;
          parent_id?: string | null;
          comment_text: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          content_id?: string;
          author_id?: string | null;
          parent_id?: string | null;
          comment_text?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      content_views: {
        Row: {
          id: string;
          content_id: string;
          profile_id: string | null;
          viewed_at: string;
        };
        Insert: {
          id?: string;
          content_id: string;
          profile_id?: string | null;
          viewed_at?: string;
        };
        Update: {
          id?: string;
          content_id?: string;
          profile_id?: string | null;
          viewed_at?: string;
        };
      };
      community_join_requests: {
        Row: {
          id: string;
          community_id: string;
          profile_id: string;
          status: string;
          message: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          community_id: string;
          profile_id: string;
          status?: string;
          message?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          community_id?: string;
          profile_id?: string;
          status?: string;
          message?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
  };
}
