import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextResponse } from 'next/server';
import type { Database } from '@/types/supabase';

type CommunityInsert = Database['public']['Tables']['communities']['Insert'];

export async function POST(request: Request) {
  const supabase = createSupabaseServerClient();
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  let communityData: Partial<Omit<CommunityInsert, 'owner_id'>>; // owner_id will be set from user.id
  try {
    communityData = await request.json();
  } catch (e) {
    return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
  }

  const { name, slug, is_private, theme } = communityData;

  if (!name || typeof name !== 'string' || name.trim().length === 0) {
    return NextResponse.json({ error: 'Name is required and must be a non-empty string' }, { status: 400 });
  }
  if (!slug || typeof slug !== 'string' || slug.trim().length === 0) {
    return NextResponse.json({ error: 'Slug is required and must be a non-empty string' }, { status: 400 });
  }
   if (!/^[a-z0-9]+(?:-[a-z0-9]+)*$/.test(slug)) {
    return NextResponse.json({ error: 'Slug must be lowercase alphanumeric with hyphens and no leading/trailing hyphens.' }, { status: 400 });
  }
  if (typeof is_private !== 'boolean') {
    // Default to false if not provided, or handle as error if strictly required
    // For now, let's assume it might be optional in body but required in DB (DB has default)
    // However, the form sends it, so it should be present.
    return NextResponse.json({ error: 'is_private is required and must be a boolean' }, { status: 400 });
  }

  const { data: existingCommunity, error: slugError } = await supabase
    .from('communities')
    .select('slug')
    .eq('slug', slug)
    .maybeSingle();

  if (slugError && slugError.code !== 'PGRST116') { 
    console.error('Error checking slug uniqueness:', slugError);
    return NextResponse.json({ error: 'Internal server error while checking slug' }, { status: 500 });
  }
  if (existingCommunity) {
    return NextResponse.json({ error: 'Slug already taken' }, { status: 409 });
  }

  const communityToInsert: CommunityInsert = {
      name, 
      slug, 
      is_private, 
      owner_id: user.id, 
      theme: theme && typeof theme === 'object' ? theme : {},
      default_language: 'en', // Set a default if not provided by client
      // Ensure all non-nullable fields in 'communities' table have values or defaults
  };

  const { data: newCommunity, error: insertError } = await supabase
    .from('communities')
    .insert(communityToInsert) // Pass as an object, not an array
    .select()
    .single();

  if (insertError) {
    console.error('Error inserting community:', insertError);
    if (insertError.code === '23505') { 
        return NextResponse.json({ error: 'Community already exists or unique constraint violated (e.g. slug).' }, { status: 409 });
    }
    return NextResponse.json({ error: 'Failed to create community', details: insertError.message }, { status: 500 });
  }

  return NextResponse.json(newCommunity, { status: 201 });
}
