import { createSupabaseServerClient } from "@/lib/supabase/server";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import { getUserProfileById } from "@/lib/supabase/queries"; 
import type { Database } from "@/types/supabase";

type ProfileRow = Database['public']['Tables']['profiles']['Row'];

export default async function DashboardLayoutWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = createSupabaseServerClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();

  let profile: ProfileRow | null = null;
  
  if (session?.user?.id) { // Ensure session and user.id exist
    const { data: fetchedProfile, error: profileError } = await getUserProfileById(supabase, session.user.id);
    if (profileError) {
      console.error("Error fetching profile in dashboard layout:", profileError);
    }
    profile = fetchedProfile;
  } else {
    // This case should ideally be handled by middleware redirecting to login if session is required
    console.warn("Dashboard layout: No session or user ID found. User might be redirected by middleware.");
    // If not using middleware for this specific path and session is truly optional, profile will remain null.
  }

  return <DashboardLayout user={profile}>{children}</DashboardLayout>;
}
