"use client";

import { useState } from "react";
import {
  Box,
  Heading,
  Text,
  SimpleGrid,
  Input,
  InputGroup,
  InputLeftElement,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Flex,
  Tag,
  TagLabel,
  HStack,
  Container,
  useColorModeValue,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { useDirection } from "@/lib/contexts/DirectionContext";
import { Search } from "lucide-react";
import TemplateCard from "@/components/communities/TemplateCard";
import LanguageSwitcher from "@/components/auth/LanguageSwitcher";

const templates = [
  {
    id: "customer-self-service",
    name: "Customer Self-Service Portal",
    description:
      "A support community for your customers with help center, FAQs, and discussion forums.",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1600880292203-757bb62b4baf?w=500&q=80",
    category: "Business",
    tags: ["Support", "Help Center", "Knowledge Base"],
    popularity: 95,
  },
  {
    id: "athletes-community",
    name: "Athletes Community",
    description:
      "A fitness-focused community for athletes to share workouts, track progress, and connect.",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1517836357463-d25dfeac3438?w=500&q=80",
    category: "Fitness",
    tags: ["Sports", "Workouts", "Health"],
    popularity: 88,
  },
  {
    id: "reddit-like-community",
    name: "Reddit-like Community",
    description:
      "A discussion-focused community with posts, upvotes, and threaded comments.",
    thumbnailUrl:
      "https://images.unsplash.com/photo-**********-536de3962603?w=500&q=80",
    category: "Social",
    tags: ["Discussions", "Forums", "Social"],
    popularity: 92,
  },
  {
    id: "learning-academy",
    name: "Learning Academy",
    description:
      "An educational platform with courses, lessons, and certification paths.",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1501504905252-473c47e087f8?w=500&q=80",
    category: "Education",
    tags: ["Courses", "Learning", "Education"],
    popularity: 90,
  },
  {
    id: "company-intranet",
    name: "Company Intranet",
    description:
      "An internal platform for company announcements, resources, and team collaboration.",
    thumbnailUrl:
      "https://images.unsplash.com/photo-**********-b413da4baf72?w=500&q=80",
    category: "Business",
    tags: ["Internal", "Team", "Collaboration"],
    popularity: 85,
  },
  {
    id: "creator-hub",
    name: "Creator Hub",
    description:
      "A platform for content creators to share work, get feedback, and build an audience.",
    thumbnailUrl:
      "https://images.unsplash.com/photo-1533750516457-a7f992034fec?w=500&q=80",
    category: "Creative",
    tags: ["Portfolio", "Showcase", "Creative"],
    popularity: 82,
  },
];

const categories = [
  { id: "all", name: "All Templates" },
  { id: "business", name: "Business" },
  { id: "social", name: "Social" },
  { id: "education", name: "Education" },
  { id: "fitness", name: "Fitness" },
  { id: "creative", name: "Creative" },
];

export default function CommunitiesTemplatesHub() {
  const { t } = useTranslation();
  const { direction } = useDirection();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  const filterTemplates = () => {
    return templates.filter((template) => {
      const matchesSearch = !searchQuery
        ? true
        : template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          template.description
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          template.tags.some((tag) =>
            tag.toLowerCase().includes(searchQuery.toLowerCase()),
          );

      const matchesCategory =
        selectedCategory === "all" ||
        template.category.toLowerCase() === selectedCategory.toLowerCase();

      return matchesSearch && matchesCategory;
    });
  };

  const filteredTemplates = filterTemplates();

  return (
    <Box dir={direction}>
      <Flex justify="flex-end" mb={4}>
        <LanguageSwitcher />
      </Flex>
      <Box mb={8}>
        <Heading as="h1" size="xl" mb={2}>
          {t("templates.title", "Community Templates")}
        </Heading>
        <Text color="gray.600" fontSize="lg">
          {t(
            "templates.description",
            "Choose from our pre-built templates to quickly set up your community",
          )}
        </Text>
      </Box>

      <Flex
        direction={{ base: "column", md: "row" }}
        gap={4}
        mb={6}
        align={{ base: "stretch", md: "center" }}
      >
        <InputGroup maxW={{ base: "full", md: "320px" }}>
          <InputLeftElement pointerEvents="none">
            <Search size={18} color="gray.300" />
          </InputLeftElement>
          <Input
            placeholder={t(
              "templates.searchPlaceholder",
              "Search templates...",
            )}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </InputGroup>

        <HStack
          spacing={2}
          overflow="auto"
          flexWrap={{ base: "nowrap", md: "wrap" }}
        >
          {categories.map((category) => (
            <Tag
              key={category.id}
              size="lg"
              borderRadius="full"
              variant={selectedCategory === category.id ? "solid" : "subtle"}
              colorScheme="teal"
              cursor="pointer"
              onClick={() => setSelectedCategory(category.id)}
              px={4}
              py={2}
            >
              <TagLabel>{category.name}</TagLabel>
            </Tag>
          ))}
        </HStack>
      </Flex>

      <Tabs colorScheme="teal" mb={8}>
        <TabList>
          <Tab>{t("templates.popular", "Popular")}</Tab>
          <Tab>{t("templates.newest", "Newest")}</Tab>
          <Tab>{t("templates.trending", "Trending")}</Tab>
        </TabList>

        <TabPanels>
          <TabPanel px={0}>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
              {filteredTemplates
                .sort((a, b) => b.popularity - a.popularity)
                .map((template) => (
                  <TemplateCard
                    key={template.id}
                    id={template.id}
                    name={template.name}
                    description={template.description}
                    thumbnailUrl={template.thumbnailUrl}
                    tags={template.tags}
                    category={template.category}
                  />
                ))}
            </SimpleGrid>
          </TabPanel>
          <TabPanel px={0}>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
              {filteredTemplates.map((template) => (
                <TemplateCard
                  key={template.id}
                  id={template.id}
                  name={template.name}
                  description={template.description}
                  thumbnailUrl={template.thumbnailUrl}
                  tags={template.tags}
                  category={template.category}
                />
              ))}
            </SimpleGrid>
          </TabPanel>
          <TabPanel px={0}>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
              {filteredTemplates
                .sort((a, b) => b.popularity - a.popularity)
                .slice(0, 3)
                .map((template) => (
                  <TemplateCard
                    key={template.id}
                    id={template.id}
                    name={template.name}
                    description={template.description}
                    thumbnailUrl={template.thumbnailUrl}
                    tags={template.tags}
                    category={template.category}
                  />
                ))}
            </SimpleGrid>
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  );
}
