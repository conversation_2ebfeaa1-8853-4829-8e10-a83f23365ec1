"use client";

import { useDirection } from "@/lib/contexts/DirectionContext";
import { useTranslation } from "react-i18next";

const Footer = () => {
  const { t } = useTranslation();
  const { direction } = useDirection();

  return (
    <footer className="py-10 border-t border-gray-200" dir={direction}>
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-center md:items-start text-center md:text-left">
          <div className="mb-6 md:mb-0">
            <h3 className="text-lg font-bold text-teal-600 mb-2">
              Wuilt
            </h3>
            <p className="text-gray-600 text-sm">
              © {new Date().getFullYear()} Wuilt. All rights reserved.
            </p>
          </div>
          <div className="flex flex-col md:flex-row space-y-6 md:space-y-0 md:space-x-6">
            <div className="space-y-2 flex flex-col items-center md:items-start">
              <p className="font-bold">{t("common:footer.product", "Product")}</p>
              <a href="#" className="text-gray-600 hover:text-gray-800">
                {t("common:footer.features", "Features")}
              </a>
              <a href="#" className="text-gray-600 hover:text-gray-800">
                {t("common:footer.pricing", "Pricing")}
              </a>
              <a href="#" className="text-gray-600 hover:text-gray-800">
                {t("common:footer.testimonials", "Testimonials")}
              </a>
            </div>
            <div className="space-y-2 flex flex-col items-center md:items-start">
              <p className="font-bold">{t("common:footer.company", "Company")}</p>
              <a href="#" className="text-gray-600 hover:text-gray-800">
                {t("common:footer.about", "About")}
              </a>
              <a href="#" className="text-gray-600 hover:text-gray-800">
                {t("common:footer.blog", "Blog")}
              </a>
              <a href="#" className="text-gray-600 hover:text-gray-800">
                {t("common:footer.contact", "Contact")}
              </a>
            </div>
            <div className="space-y-2 flex flex-col items-center md:items-start">
              <p className="font-bold">{t("common:footer.legal", "Legal")}</p>
              <a href="#" className="text-gray-600 hover:text-gray-800">
                {t("common:footer.privacy", "Privacy")}
              </a>
              <a href="#" className="text-gray-600 hover:text-gray-800">
                {t("common:footer.terms", "Terms")}
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
