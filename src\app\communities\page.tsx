import { createSupabaseServerClient } from "@/lib/supabase/server";
import { redirect } from "next/navigation";
import CommunitiesPage from "@/components/communities/CommunitiesPage";

export default async function Communities() {
  const supabase = createSupabaseServerClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (!session) {
    redirect("/login");
  }

  // Get user profile
  const { data: profile } = await supabase
    .from("profiles")
    .select("*")
    .eq("id", session.user.id)
    .single();

  // Get user's communities
  const { data: userCommunities } = await supabase
    .from("community_members")
    .select(
      `
      community:communities(id, name, description, logo_url, banner_url, is_private, slug)
    `,
    )
    .eq("profile_id", session.user.id);

  // Get all public communities
  const { data: publicCommunities } = await supabase
    .from("communities")
    .select("*")
    .eq("is_private", false)
    .limit(10);

  return (
    <CommunitiesPage
      userCommunities={userCommunities?.map((item) => item.community) || []}
      publicCommunities={publicCommunities || []}
      language={profile?.language || "en"}
    />
  );
}
