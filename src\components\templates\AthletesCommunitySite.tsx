"use client";

import React from "react";
import {
  Box,
  Flex,
  Heading,
  Text,
  Button,
  SimpleGrid,
  Card,
  CardBody,
  Avatar,
  Badge,
  HStack,
  VStack,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Image,
  Link,
  Divider,
  useColorModeValue,
  Icon,
} from "@chakra-ui/react";
import {
  Search,
  Heart,
  MessageSquare,
  Calendar,
  Clock,
  MapPin,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import LanguageSwitcher from "@/components/auth/LanguageSwitcher";

export default function AthletesCommunitySite() {
  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");
  const { t } = useTranslation();

  return (
    <Box>
      {/* Header */}
      <Flex
        as="header"
        align="center"
        justify="space-between"
        py={4}
        px={8}
        borderBottom="1px"
        borderColor={borderColor}
        bg={bgColor}
      >
        <Flex align="center">
          <Box as="span" fontSize="2xl" fontWeight="bold" color="black" mr={2}>
            FitMode
          </Box>
          <HStack spacing={6} ml={10} display={{ base: "none", md: "flex" }}>
            <Link fontWeight="medium">Home</Link>
            <Link fontWeight="medium">Workouts</Link>
            <Link fontWeight="medium">Activity</Link>
            <Link fontWeight="medium">Programs</Link>
            <Link fontWeight="medium">Events</Link>
          </HStack>
        </Flex>
        <Flex align="center" gap={4}>
          <Icon as={Search} boxSize={5} />
          <Button size="sm" variant="ghost" p={0}>
            <Icon as={Search} boxSize={5} />
          </Button>
          <Button size="sm" colorPalette="black" variant="outline">
            {t("auth.signIn", "Login")}
          </Button>
          <LanguageSwitcher />
        </Flex>
      </Flex>

      {/* New Workouts Section */}
      <Box py={8} px={8}>
        <Flex justify="space-between" align="center" mb={6}>
          <Heading as="h2" size="lg">
            New Workouts
          </Heading>
          <Link color="blue.500" fontWeight="medium">
            View All
          </Link>
        </Flex>

        <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
          {/* Workout Card 1 */}
          <Card overflow="hidden" borderRadius="lg" borderColor={borderColor}>
            <Box position="relative">
              <Image
                src="https://images.unsplash.com/photo-1518611012118-696072aa579a?w=500&q=80"
                alt="Breathing Pattern Reset"
                height="200px"
                width="100%"
                objectFit="cover"
              />
              <Box
                position="absolute"
                bottom={0}
                left={0}
                right={0}
                bg="rgba(0,0,0,0.7)"
                p={4}
                color="white"
              >
                <Heading size="md">Breathing Pattern Reset</Heading>
              </Box>
            </Box>
            <CardBody>
              <Badge mb={2} colorPalette="gray">
                Mindset
              </Badge>
              <Heading size="md" mb={2}>
                Breathing Pattern Reset
              </Heading>
              <Badge colorPalette="green" mb={4}>
                Beginner Level
              </Badge>
            </CardBody>
          </Card>

          {/* Workout Card 2 */}
          <Card overflow="hidden" borderRadius="lg" borderColor={borderColor}>
            <Box position="relative">
              <Image
                src="https://images.unsplash.com/photo-1552196563-55cd4e45efb3?w=500&q=80"
                alt="Mind Over Matter Flow"
                height="200px"
                width="100%"
                objectFit="cover"
              />
              <Box
                position="absolute"
                bottom={0}
                left={0}
                right={0}
                bg="rgba(0,0,0,0.7)"
                p={4}
                color="white"
              >
                <Heading size="md">Mind Over Matter Flow</Heading>
              </Box>
            </Box>
            <CardBody>
              <Badge mb={2} colorScheme="gray">
                Yoga
              </Badge>
              <Heading size="md" mb={2}>
                Mind-Over-Matter Flow
              </Heading>
              <Badge colorScheme="purple" mb={4}>
                Advanced Level
              </Badge>
            </CardBody>
          </Card>

          {/* Featured Workout Card */}
          <Card overflow="hidden" borderRadius="lg" borderColor={borderColor}>
            <Box position="relative" height="100%">
              <Image
                src="https://images.unsplash.com/photo-1434682881908-b43d0467b798?w=500&q=80"
                alt="Featured Workout"
                height="100%"
                width="100%"
                objectFit="cover"
              />
              <Box
                position="absolute"
                top={0}
                left={0}
                right={0}
                bottom={0}
                bg="rgba(0,0,0,0.5)"
                p={6}
                display="flex"
                flexDirection="column"
                justifyContent="flex-end"
                color="white"
              >
                <VStack align="flex-start" spacing={2}>
                  <Text fontWeight="bold">Yesterday</Text>
                  <Text fontWeight="bold">You Said</Text>
                  <Text fontWeight="bold">Tomorrow</Text>
                  <Button size="sm" colorScheme="whiteAlpha" mt={2}>
                    Start now!
                  </Button>
                </VStack>
              </Box>
            </Box>
          </Card>
        </SimpleGrid>
      </Box>

      {/* Leaderboard Section */}
      <Box py={8} px={8} bg="gray.50">
        <Box maxW="container.xl" mx="auto">
          <Flex justify="space-between" align="center" mb={6}>
            <Heading as="h2" size="lg">
              Leaderboard
            </Heading>
            <Icon as={Search} boxSize={5} />
          </Flex>

          <Tabs variant="soft-rounded" colorScheme="gray" mb={4}>
            <TabList>
              <Tab>All time</Tab>
              <Tab>Month</Tab>
              <Tab>Week</Tab>
            </TabList>
          </Tabs>

          <Card borderColor={borderColor} shadow="sm">
            <CardBody p={0}>
              {/* Leaderboard Entry 1 */}
              <Flex
                p={4}
                borderBottomWidth="1px"
                borderColor={borderColor}
                align="center"
              >
                <Text fontWeight="bold" mr={4}>
                  1
                </Text>
                <Avatar
                  size="sm"
                  src="https://api.dicebear.com/7.x/avataaars/svg?seed=Ethan"
                  mr={4}
                />
                <Text flex="1">Ethan Blake</Text>
                <Text fontWeight="bold">28</Text>
              </Flex>

              {/* Leaderboard Entry 2 */}
              <Flex
                p={4}
                borderBottomWidth="1px"
                borderColor={borderColor}
                align="center"
              >
                <Text fontWeight="bold" mr={4}>
                  2
                </Text>
                <Avatar
                  size="sm"
                  src="https://api.dicebear.com/7.x/avataaars/svg?seed=Sophia"
                  mr={4}
                />
                <Text flex="1">Sophia Harper</Text>
                <Text fontWeight="bold">26</Text>
              </Flex>

              {/* Leaderboard Entry 3 */}
              <Flex p={4} align="center">
                <Text fontWeight="bold" mr={4}>
                  3
                </Text>
                <Avatar
                  size="sm"
                  src="https://api.dicebear.com/7.x/avataaars/svg?seed=Liam"
                  mr={4}
                />
                <Text flex="1">Liam Prescott</Text>
                <Text fontWeight="bold">23</Text>
              </Flex>
            </CardBody>
          </Card>
        </Box>
      </Box>

      {/* Upcoming Events */}
      <Box py={8} px={8}>
        <Box maxW="container.xl" mx="auto">
          <Heading as="h2" size="lg" mb={6}>
            Upcoming events
          </Heading>

          <SimpleGrid columns={{ base: 1, md: 1 }} spacing={4}>
            {/* Event 1 */}
            <Card borderColor={borderColor} shadow="sm">
              <CardBody>
                <Flex direction={{ base: "column", sm: "row" }} gap={4}>
                  <Box flex="1">
                    <Heading as="h3" size="md" mb={2}>
                      Sunset Yoga & Paddleboarding Retreat
                    </Heading>
                    <Text color="gray.600" mb={3}>
                      Sunset Yoga & Paddleboarding Retreat's a calming outdoor
                      event combining yoga and water activities for all skill
                      levels.
                    </Text>
                    <HStack spacing={4} mb={2}>
                      <Flex align="center">
                        <Icon as={Calendar} size={14} mr={1} />
                        <Text fontSize="sm">04/23/2024</Text>
                      </Flex>
                      <Flex align="center">
                        <Icon as={Clock} size={14} mr={1} />
                        <Text fontSize="sm">08:45 AM</Text>
                      </Flex>
                      <Flex align="center">
                        <Icon as={MapPin} size={14} mr={1} />
                        <Text fontSize="sm">Toronto</Text>
                      </Flex>
                    </HStack>
                    <Button size="sm" colorScheme="blackAlpha" mt={2}>
                      Register now →
                    </Button>
                  </Box>
                </Flex>
              </CardBody>
            </Card>

            {/* Event 2 */}
            <Card borderColor={borderColor} shadow="sm">
              <CardBody>
                <Flex direction={{ base: "column", sm: "row" }} gap={4}>
                  <Box flex="1">
                    <Heading as="h3" size="md" mb={2}>
                      Run & Recharge: Mindful Running Meetup
                    </Heading>
                    <Text color="gray.600" mb={3}>
                      Run & Recharge is an event designed for runners who want
                      to combine physical exercise with mindfulness practices.
                    </Text>
                    <HStack spacing={4} mb={2}>
                      <Flex align="center">
                        <Icon as={Calendar} size={14} mr={1} />
                        <Text fontSize="sm">11/17/2024</Text>
                      </Flex>
                      <Flex align="center">
                        <Icon as={Clock} size={14} mr={1} />
                        <Text fontSize="sm">09:00 PM</Text>
                      </Flex>
                      <Flex align="center">
                        <Icon as={MapPin} size={14} mr={1} />
                        <Text fontSize="sm">Sydney</Text>
                      </Flex>
                    </HStack>
                    <Button size="sm" colorScheme="blackAlpha" mt={2}>
                      Register now →
                    </Button>
                  </Box>
                </Flex>
              </CardBody>
            </Card>

            {/* Event 3 */}
            <Card borderColor={borderColor} shadow="sm">
              <CardBody>
                <Flex direction={{ base: "column", sm: "row" }} gap={4}>
                  <Box flex="1">
                    <Heading as="h3" size="md" mb={2}>
                      LA FMC Meetup
                    </Heading>
                    <Text color="gray.600" mb={3}>
                      Hey FitMode Fam! 👋 We had an amazing time at the Los
                      Angeles FitMode Community meetup last weekend!
                    </Text>
                    <HStack spacing={4} mb={2}>
                      <Flex align="center">
                        <Icon as={Calendar} size={14} mr={1} />
                        <Text fontSize="sm">11/06/2024</Text>
                      </Flex>
                      <Flex align="center">
                        <Icon as={Clock} size={14} mr={1} />
                        <Text fontSize="sm">12:30 PM</Text>
                      </Flex>
                      <Flex align="center">
                        <Icon as={MapPin} size={14} mr={1} />
                        <Text fontSize="sm">Los Angeles</Text>
                      </Flex>
                    </HStack>
                    <Button size="sm" colorScheme="blackAlpha" mt={2}>
                      Register now →
                    </Button>
                  </Box>
                </Flex>
              </CardBody>
            </Card>
          </SimpleGrid>
        </Box>
      </Box>

      {/* Who to follow */}
      <Box py={8} px={8} bg="gray.50">
        <Box maxW="container.xl" mx="auto">
          <Heading as="h2" size="lg" mb={6}>
            Who to follow
          </Heading>

          <SimpleGrid columns={{ base: 1, sm: 2, md: 4 }} spacing={4}>
            {/* Person 1 */}
            <Card borderColor={borderColor} shadow="sm">
              <CardBody>
                <Flex direction="column" align="center" textAlign="center">
                  <Avatar
                    size="lg"
                    src="https://api.dicebear.com/7.x/avataaars/svg?seed=Elaya"
                    mb={3}
                  />
                  <Heading as="h3" size="md" mb={1}>
                    Elaya
                  </Heading>
                  <Button size="sm" colorScheme="blackAlpha" mt={2} w="full">
                    Follow
                  </Button>
                </Flex>
              </CardBody>
            </Card>

            {/* Person 2 */}
            <Card borderColor={borderColor} shadow="sm">
              <CardBody>
                <Flex direction="column" align="center" textAlign="center">
                  <Avatar
                    size="lg"
                    src="https://api.dicebear.com/7.x/avataaars/svg?seed=Ethan"
                    mb={3}
                  />
                  <Heading as="h3" size="md" mb={1}>
                    Ethan Blake
                  </Heading>
                  <Button size="sm" colorScheme="blackAlpha" mt={2} w="full">
                    Follow
                  </Button>
                </Flex>
              </CardBody>
            </Card>

            {/* Person 3 */}
            <Card borderColor={borderColor} shadow="sm">
              <CardBody>
                <Flex direction="column" align="center" textAlign="center">
                  <Avatar
                    size="lg"
                    src="https://api.dicebear.com/7.x/avataaars/svg?seed=Sophia"
                    mb={3}
                  />
                  <Heading as="h3" size="md" mb={1}>
                    Sophia Harper
                  </Heading>
                  <Button size="sm" colorScheme="blackAlpha" mt={2} w="full">
                    Follow
                  </Button>
                </Flex>
              </CardBody>
            </Card>

            {/* Person 4 */}
            <Card borderColor={borderColor} shadow="sm">
              <CardBody>
                <Flex direction="column" align="center" textAlign="center">
                  <Avatar
                    size="lg"
                    src="https://api.dicebear.com/7.x/avataaars/svg?seed=Liam"
                    mb={3}
                  />
                  <Heading as="h3" size="md" mb={1}>
                    Liam Prescott
                  </Heading>
                  <Button size="sm" colorScheme="blackAlpha" mt={2} w="full">
                    Follow
                  </Button>
                </Flex>
              </CardBody>
            </Card>
          </SimpleGrid>

          <Box textAlign="center" mt={6}>
            <Button variant="outline">Show more</Button>
          </Box>
        </Box>
      </Box>

      {/* Recent Activities */}
      <Box py={8} px={8}>
        <Box maxW="container.xl" mx="auto">
          <Heading as="h2" size="lg" mb={6}>
            Recent Activities
          </Heading>

          <Flex justify="space-between" mb={6}>
            <HStack spacing={4}>
              <Button size="sm" variant="ghost">
                Activity Type
              </Button>
              <Button size="sm" variant="ghost">
                Date & time
              </Button>
              <Button size="sm" variant="ghost">
                Duration
              </Button>
            </HStack>
          </Flex>

          {/* Activity 1 */}
          <Card borderColor={borderColor} shadow="sm" mb={6}>
            <CardBody>
              <Flex justify="space-between" mb={4}>
                <Flex align="center">
                  <Avatar
                    size="md"
                    src="https://api.dicebear.com/7.x/avataaars/svg?seed=Liam"
                    mr={4}
                  />
                  <Box>
                    <Heading as="h3" size="md">
                      Liam Prescott
                    </Heading>
                    <Text fontSize="sm" color="gray.500">
                      6 months ago · Tennis Player
                    </Text>
                  </Box>
                </Flex>
                <HStack>
                  <Badge colorScheme="blue">Running</Badge>
                  <Badge colorScheme="gray">30 Minutes</Badge>
                </HStack>
              </Flex>

              <Heading as="h4" size="md" mb={2}>
                Morning Run
              </Heading>

              <Text mb={4}>
                Conquered the Pacific Spirit Park trails with a 6k run. The lush
                greenery and peaceful vibes made it a refreshing escape. Nature
                is the best workout partner! #RunningVancouver #StayActive
              </Text>

              <Flex justify="space-between">
                <HStack>
                  <Button
                    leftIcon={<Icon as={Heart} size={16} />}
                    variant="ghost"
                    size="sm"
                  >
                    Like
                  </Button>
                </HStack>
                <Button
                  rightIcon={<Icon as={MessageSquare} size={16} />}
                  variant="ghost"
                  size="sm"
                >
                  Share
                </Button>
              </Flex>
            </CardBody>
          </Card>

          {/* Activity 2 */}
          <Card borderColor={borderColor} shadow="sm" mb={6}>
            <CardBody>
              <Flex justify="space-between" mb={4}>
                <Flex align="center">
                  <Avatar
                    size="md"
                    src="https://api.dicebear.com/7.x/avataaars/svg?seed=Sophia"
                    mr={4}
                  />
                  <Box>
                    <Heading as="h3" size="md">
                      Sophia Harper
                    </Heading>
                    <Text fontSize="sm" color="gray.500">
                      5 months ago · Strength in balance, joy in motion.
                    </Text>
                  </Box>
                </Flex>
              </Flex>

              <Text mb={4}>
                Sounds like an amazing run! The Pacific Spirit Park trails are
                definitely the perfect blend of nature and fitness. Keep it up!
                🌲 🏃‍♂️
              </Text>

              <Flex justify="space-between">
                <HStack>
                  <Button
                    leftIcon={<Icon as={Heart} size={16} />}
                    variant="ghost"
                    size="sm"
                  >
                    Like
                  </Button>
                </HStack>
              </Flex>
            </CardBody>
          </Card>

          {/* Activity 3 */}
          <Card borderColor={borderColor} shadow="sm">
            <CardBody>
              <Flex justify="space-between" mb={4}>
                <Flex align="center">
                  <Avatar
                    size="md"
                    src="https://api.dicebear.com/7.x/avataaars/svg?seed=Max"
                    mr={4}
                  />
                  <Box>
                    <Heading as="h3" size="md">
                      Max Spencer
                    </Heading>
                    <Text fontSize="sm" color="gray.500">
                      4 months ago · Runner 🏃 🏃‍♂️ 🏃‍♀️
                    </Text>
                  </Box>
                </Flex>
                <HStack>
                  <Badge colorScheme="green">Cycling</Badge>
                  <Badge colorScheme="gray">30 Minutes</Badge>
                </HStack>
              </Flex>

              <Heading as="h4" size="md" mb={2}>
                Daily Cycling to University
              </Heading>

              <Text mb={4}>
                Pedaling my way to a healthier mind and body—daily cycling to
                university is my little escape, my workout, and my moment with
                the world. 🚲 🌍 #CyclingLife #EcoCommute #StayActive
              </Text>

              <Flex justify="space-between">
                <HStack>
                  <Button
                    leftIcon={<Icon as={Heart} size={16} />}
                    variant="ghost"
                    size="sm"
                  >
                    Like
                  </Button>
                </HStack>
                <Button
                  rightIcon={<Icon as={MessageSquare} size={16} />}
                  variant="ghost"
                  size="sm"
                >
                  Share
                </Button>
              </Flex>
            </CardBody>
          </Card>
        </Box>
      </Box>

      {/* Footer */}
      <Box as="footer" bg="gray.100" py={10} px={8}>
        <Box maxW="container.xl" mx="auto">
          <SimpleGrid columns={{ base: 1, md: 4 }} spacing={8}>
            <Box>
              <Heading size="md" mb={4}>
                FitMode
              </Heading>
              <Text color="gray.600" mb={4}>
                Connect, train, and grow with the FitMode community.
              </Text>
            </Box>
            <Box>
              <Heading size="sm" mb={4}>
                Features
              </Heading>
              <Flex direction="column" gap={2}>
                <Link>Workouts</Link>
                <Link>Programs</Link>
                <Link>Challenges</Link>
                <Link>Events</Link>
              </Flex>
            </Box>
            <Box>
              <Heading size="sm" mb={4}>
                Community
              </Heading>
              <Flex direction="column" gap={2}>
                <Link>Activity Feed</Link>
                <Link>Leaderboards</Link>
                <Link>Groups</Link>
                <Link>Forums</Link>
              </Flex>
            </Box>
            <Box>
              <Heading size="sm" mb={4}>
                Support
              </Heading>
              <Flex direction="column" gap={2}>
                <Link>Help Center</Link>
                <Link>Contact Us</Link>
                <Link>Privacy Policy</Link>
                <Link>Terms of Service</Link>
              </Flex>
            </Box>
          </SimpleGrid>
          <Divider my={6} borderColor="gray.300" />
          <Text color="gray.600" fontSize="sm">
            © 2023 FitMode. All rights reserved.
          </Text>
        </Box>
      </Box>
    </Box>
  );
}
