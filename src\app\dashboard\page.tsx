import { createSupabaseServerClient } from "@/lib/supabase/server";
import { getUserProfileById } from "@/lib/supabase/queries";
import type { Database } from "@/types/supabase";
import type { ProfileRow } from "@/types/app.types"; // Import ProfileRow from app.types

export interface CommunityForDashboardView {
  id: string;
  name: string;
  description: string | null;
  logo_url: string | null;
  banner_url: string | null;
  is_private: boolean;
  slug: string;
}

export default async function DashboardPage() {
  const supabase = createSupabaseServerClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();

  let profile: ProfileRow | null = null;
  if (session?.user?.id) {
    const { data: fetchedProfile, error: profileError } = await getUserProfileById(supabase, session.user.id);
    if (profileError) {
      console.error("Error fetching profile in dashboard page:", profileError);
    }
    profile = fetchedProfile;
  } else {
    console.warn("Dashboard page: No session or user ID found.");
  }

  const { data: communitiesData, error: communitiesError } = await supabase
    .from("community_members")
    .select(
      `
      community:communities!inner(id, name, description, logo_url, banner_url, is_private, slug)
    `
    )
    .eq("profile_id", session?.user?.id || '')
    .returns<{ community: CommunityForDashboardView }[]>();

  if (communitiesError) {
    console.error("Error fetching communities:", communitiesError);
  }

  const userCommunities: CommunityForDashboardView[] = communitiesData?.map((item) => item.community).filter(Boolean) || [];

  return (
    <div className="max-w-7xl mx-auto py-6">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {profile?.full_name ?? "User"}!
          </h1>
          <p className="text-gray-600">Here's what's happening in your communities</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-900">Communities</h3>
            <p className="text-3xl font-bold text-teal-600">{userCommunities.length}</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-900">Active Spaces</h3>
            <p className="text-3xl font-bold text-teal-600">0</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-900">Messages</h3>
            <p className="text-3xl font-bold text-teal-600">0</p>
          </div>
        </div>

        {/* Communities List */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">Your Communities</h2>
          </div>
          <div className="p-6">
            {userCommunities.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {userCommunities.map((community) => (
                  <div key={community.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <h3 className="font-semibold text-gray-900">{community.name}</h3>
                    <p className="text-sm text-gray-600 mt-1">{community.description}</p>
                    <div className="mt-3">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        community.is_private ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                      }`}>
                        {community.is_private ? 'Private' : 'Public'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">You haven't joined any communities yet.</p>
                <a href="/communities" className="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700">
                  Browse Communities
                </a>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
