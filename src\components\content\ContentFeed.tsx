"use client";

import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import {
  Heart,
  MessageSquare,
  Share2,
  BookmarkPlus,
  MoreHorizontal,
  Search,
  Filter,
} from "lucide-react";

interface ContentItem {
  id: string;
  type: "post" | "article" | "course";
  title: string;
  excerpt: string;
  author: {
    name: string;
    avatar: string;
  };
  publishedAt: string;
  likes: number;
  comments: number;
  tags: string[];
  image?: string;
}

interface ContentFeedProps {
  items?: ContentItem[];
  title?: string;
  showFilters?: boolean;
}

const ContentFeed = ({
  items = [
    {
      id: "1",
      type: "post",
      title: "Getting Started with Community Building",
      excerpt:
        "Learn the fundamentals of building an engaged online community from scratch.",
      author: {
        name: "Sarah Johnson",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Sarah",
      },
      publishedAt: "2023-10-15",
      likes: 24,
      comments: 8,
      tags: ["community", "beginners"],
      image:
        "https://images.unsplash.com/photo-1556761175-b413da4baf72?w=600&q=80",
    },
    {
      id: "2",
      type: "article",
      title: "Advanced Moderation Techniques",
      excerpt:
        "Discover proven strategies for effective community moderation and conflict resolution.",
      author: {
        name: "Michael Chen",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Michael",
      },
      publishedAt: "2023-10-10",
      likes: 42,
      comments: 15,
      tags: ["moderation", "advanced"],
      image:
        "https://images.unsplash.com/photo-1552664730-d307ca884978?w=600&q=80",
    },
    {
      id: "3",
      type: "course",
      title: "Community Management Masterclass",
      excerpt:
        "A comprehensive course covering all aspects of professional community management.",
      author: {
        name: "Aisha Rahman",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Aisha",
      },
      publishedAt: "2023-09-28",
      likes: 87,
      comments: 32,
      tags: ["course", "masterclass"],
      image:
        "https://images.unsplash.com/photo-1531482615713-2afd69097998?w=600&q=80",
    },
  ],
  title = "Content Feed",
  showFilters = true,
}: ContentFeedProps) => {
  const [activeTab, setActiveTab] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [sortBy, setSortBy] = useState<string>("recent");

  // Filter content based on active tab, search query, and sort option
  const filteredContent = items
    .filter((item) => {
      // Filter by content type
      if (activeTab !== "all" && item.type !== activeTab) return false;

      // Filter by search query
      if (
        searchQuery &&
        !item.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !item.excerpt.toLowerCase().includes(searchQuery.toLowerCase())
      ) {
        return false;
      }

      return true;
    })
    .sort((a, b) => {
      // Sort content
      if (sortBy === "recent") {
        return (
          new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
        );
      } else if (sortBy === "popular") {
        return b.likes - a.likes;
      } else if (sortBy === "comments") {
        return b.comments - a.comments;
      }
      return 0;
    });

  return (
    <div className="w-full bg-background">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold mb-2">{title}</h2>

        {showFilters && (
          <div className="space-y-4">
            <Tabs
              defaultValue="all"
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="w-full md:w-auto">
                <TabsTrigger value="all">All Content</TabsTrigger>
                <TabsTrigger value="post">Posts</TabsTrigger>
                <TabsTrigger value="article">Articles</TabsTrigger>
                <TabsTrigger value="course">Courses</TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search content..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full md:w-[180px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="recent">Most Recent</SelectItem>
                  <SelectItem value="popular">Most Popular</SelectItem>
                  <SelectItem value="comments">Most Comments</SelectItem>
                </SelectContent>
              </Select>

              <Button variant="outline" size="icon" className="md:hidden">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </div>

      {filteredContent.length > 0 ? (
        <div className="space-y-4">
          {filteredContent.map((item) => (
            <ContentCard key={item.id} item={item} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border rounded-lg">
          <p className="text-muted-foreground">
            No content found matching your criteria.
          </p>
          <Button
            variant="link"
            onClick={() => {
              setSearchQuery("");
              setActiveTab("all");
              setSortBy("recent");
            }}
          >
            Reset filters
          </Button>
        </div>
      )}
    </div>
  );
};

const ContentCard = ({ item }: { item: ContentItem }) => {
  return (
    <Card className="overflow-hidden">
      <div className="flex flex-col md:flex-row">
        {item.image && (
          <div className="md:w-1/4 h-48 md:h-auto relative">
            <img
              src={item.image}
              alt={item.title}
              className="w-full h-full object-cover"
            />
          </div>
        )}

        <CardContent
          className={`flex-1 p-6 ${!item.image ? "w-full" : "md:w-3/4"}`}
        >
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Badge
                variant={
                  item.type === "post"
                    ? "default"
                    : item.type === "article"
                      ? "secondary"
                      : "outline"
                }
              >
                {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
              </Badge>

              {item.tags.map((tag) => (
                <Badge key={tag} variant="outline">
                  {tag}
                </Badge>
              ))}
            </div>

            <Button variant="ghost" size="icon">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>

          <h3 className="text-xl font-semibold mb-2">{item.title}</h3>
          <p className="text-muted-foreground mb-4">{item.excerpt}</p>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src={item.author.avatar} alt={item.author.name} />
                <AvatarFallback>{item.author.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <span className="text-sm">{item.author.name}</span>
              <span className="text-xs text-muted-foreground">
                • {formatDate(item.publishedAt)}
              </span>
            </div>

            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon">
                <Heart className="h-4 w-4" />
                <span className="sr-only">Like</span>
              </Button>
              <span className="text-xs text-muted-foreground">
                {item.likes}
              </span>

              <Button variant="ghost" size="icon">
                <MessageSquare className="h-4 w-4" />
                <span className="sr-only">Comment</span>
              </Button>
              <span className="text-xs text-muted-foreground">
                {item.comments}
              </span>

              <Button variant="ghost" size="icon">
                <BookmarkPlus className="h-4 w-4" />
                <span className="sr-only">Save</span>
              </Button>

              <Button variant="ghost" size="icon">
                <Share2 className="h-4 w-4" />
                <span className="sr-only">Share</span>
              </Button>
            </div>
          </div>
        </CardContent>
      </div>
    </Card>
  );
};

// Helper function to format date
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  }).format(date);
};

export default ContentFeed;
