"use client";

import { useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";
import { useDirection } from "@/lib/contexts/DirectionContext";
import { Globe, Users, BookOpen, Shield } from "lucide-react";

interface FeatureProps {
  title: string;
  text: string;
  icon: React.ElementType;
}

const Feature = ({ title, text, icon: IconComponent }: FeatureProps) => {
  return (
    <div className="flex flex-col items-center text-center space-y-4">
      <div className="w-16 h-16 flex items-center justify-center text-white rounded-full bg-teal-600 mb-1">
        <IconComponent className="w-8 h-8" />
      </div>
      <h3 className="font-semibold text-lg">
        {title}
      </h3>
      <p className="text-gray-600">
        {text}
      </p>
    </div>
  );
};

export default function HomePage() {
  const { t } = useTranslation();
  const router = useRouter();
  const { direction } = useDirection();

  return (
    <div dir={direction} className="min-h-screen">
      {/* Hero Section */}
      <div className="bg-gradient-to-b from-white to-teal-50 py-20">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center gap-10">
            <div className="space-y-6 max-w-lg">
              <h1 className="text-5xl font-bold leading-tight text-teal-600">
                Build Your Learning Community
              </h1>
              <p className="text-xl text-gray-600">
                Create and manage customizable multilingual communities with
                integrated learning management capabilities.
              </p>
              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                <button
                  className="bg-teal-600 text-white px-8 py-3 rounded-lg hover:bg-teal-700 transition-colors"
                  onClick={() => router.push("/signup")}
                >
                  Sign Up
                </button>
                <button
                  className="border border-teal-600 text-teal-600 px-8 py-3 rounded-lg hover:bg-teal-50 transition-colors"
                  onClick={() => router.push("/login")}
                >
                  Sign In
                </button>
              </div>
            </div>
            <div className="relative h-80 md:h-96 w-full md:w-1/2 rounded-2xl overflow-hidden shadow-2xl">
              <img
                alt="Hero Image"
                className="w-full h-full object-cover"
                src="https://images.unsplash.com/photo-1515187029135-18ee286d815b?w=1200&q=80"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-20">
        <div className="container mx-auto px-4">
          <div className="space-y-12">
            <div className="text-center">
              <h2 className="text-3xl font-bold mb-4">
                Key Features
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Everything you need to build and grow your online community
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10">
              <Feature
                icon={Globe}
                title="Multilingual Support"
                text="Full support for both English and Arabic with RTL layout"
              />
              <Feature
                icon={Users}
                title="Community Management"
                text="Create and manage communities with customizable settings"
              />
              <Feature
                icon={BookOpen}
                title="Learning Management"
                text="Course creation with modules, lessons, and progress tracking"
              />
              <Feature
                icon={Shield}
                title="Role-Based Access"
                text="Granular permission controls for community members"
              />
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-teal-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">
            Ready to build your community?
          </h2>
          <p className="text-lg max-w-2xl mx-auto mb-8">
            Join thousands of community builders and create your own space
            today.
          </p>
          <button
            className="bg-white text-teal-600 px-8 py-3 rounded-lg hover:bg-gray-100 transition-colors"
            onClick={() => router.push("/signup")}
          >
            Get Started for Free
          </button>
        </div>
      </div>
    </div>
  );
}
