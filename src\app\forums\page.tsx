import { createSupabaseServerClient } from "@/lib/supabase/server";
import { Container } from "@chakra-ui/react";
import ForumSpacesList from "@/components/forums/ForumSpacesList";
import type { Database } from "@/types/supabase"; 
import { getUserProfileById } from "@/lib/supabase/queries"; 
import type { ProfileRow, SpaceWithCountsAndCommunity } from "@/types/app.types"; // Import from app.types

export default async function ForumsPage() {
  const supabase = createSupabaseServerClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();

  let profile: ProfileRow | null = null; 
  let profileError = null;
  let spaceMemberships: { space_id: string }[] = [];
  let communityMemberships: { community_id: string }[] = [];

  if (session) {
    const profileResult = await getUserProfileById(supabase, session.user.id);
    profile = profileResult.data;
    profileError = profileResult.error;

    if (profileError) {
      console.error("Error fetching profile in forums page:", profileError);
    }

    const spaceMembershipsResult = await supabase
      .from("space_members")
      .select("space_id")
      .eq("profile_id", session.user.id);
    spaceMemberships = spaceMembershipsResult.data || [];
    if(spaceMembershipsResult.error) console.error("Error fetching space memberships:", spaceMembershipsResult.error);


    const communityMembershipsResult = await supabase
      .from("community_members")
      .select("community_id")
      .eq("profile_id", session.user.id);
    communityMemberships = communityMembershipsResult.data || [];
    if(communityMembershipsResult.error) console.error("Error fetching community memberships:", communityMembershipsResult.error);

  } else {
    console.warn("Forums page reached without a session. Content may be limited or middleware protection might be expected.");
  }

  const { data: forumSpaces, error: forumSpacesError } = await supabase
    .from("spaces")
    .select(
      `
      *,
      community:communities(name, slug),
      posts_count:content_modules(count)
    `
    )
    .eq("type", "forum")
    .order("created_at", { ascending: false })
    .returns<SpaceWithCountsAndCommunity[]>(); 

  if (forumSpacesError) {
    console.error("Error fetching forum spaces:", forumSpacesError);
  }
  
  const memberSpaceIds = new Set(
    spaceMemberships.map((m) => m.space_id)
  );

  const memberCommunityIds = new Set(
    communityMemberships.map((m) => m.community_id)
  );

  const accessibleForumSpaces =
    forumSpaces?.filter((space: SpaceWithCountsAndCommunity) => { 
      if (space.is_private === null || space.is_private === undefined || !space.is_private) return true;
      return (
        memberSpaceIds.has(space.id) ||
        (space.community_id && memberCommunityIds.has(space.community_id))
      );
    }) || [];

  return (
    <Container maxW="container.xl" py={{base: 6, md: 10}}>
      <ForumSpacesList
        forumSpaces={accessibleForumSpaces} 
        currentUser={profile}
      />
    </Container>
  );
}
