"use client";

import { useState } from "react";
import {
  Button,
  Textarea,
  Switch,
  Box,
} from "@chakra-ui/react";
import { Input } from "@/components/ui/input";
import { <PERSON>dal, ModalOverlay, <PERSON>dalContent, <PERSON>dal<PERSON>eader, <PERSON>dal<PERSON><PERSON><PERSON>, <PERSON>dal<PERSON>ody, ModalCloseButton } from "@/components/ui/modal";
import { FormControl, FormLabel, FormErrorMessage } from "@/components/ui/form";
import { toaster } from "@/components/ui/toaster";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase/client";

interface CreatePostModalProps {
  isOpen: boolean;
  onClose: () => void;
  spaceId: string;
  currentUser: any;
}

export default function CreatePostModal({
  isOpen,
  onClose,
  spaceId,
  currentUser,
}: CreatePostModalProps) {
  const { t } = useTranslation();
  const router = useRouter();

  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [isPinned, setIsPinned] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{ title?: string; content?: string }>(
    {},
  );

  const validateForm = () => {
    const newErrors: { title?: string; content?: string } = {};
    if (!title.trim()) {
      newErrors.title = t("post.titleRequired");
    }
    if (!content.trim()) {
      newErrors.content = t("post.contentRequired");
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const { data, error } = await supabase
        .from("content_modules")
        .insert([
          {
            space_id: spaceId,
            author_id: currentUser.id,
            title,
            content: { text: content },
            type: "post",
            language: currentUser.language || "en",
            status: isPinned ? "pinned" : "published",
            metadata: {},
          },
        ])
        .select();

      if (error) throw error;

      toaster.success({
        title: t("post.createSuccess"),
        duration: 3000,
      });

      // Navigate to the new post
      if (data && data[0]) {
        router.push(`/spaces/${spaceId}/posts/${data[0].id}`);
        router.refresh();
      } else {
        onClose();
        router.refresh();
      }
    } catch (error: any) {
      toaster.error({
        title: t("post.createError"),
        description: error.message,
        duration: 5000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>{t("post.createNew")}</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <FormControl isInvalid={!!errors.title} mb={4}>
            <FormLabel>{t("post.title")}</FormLabel>
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder={t("post.titlePlaceholder")}
            />
            <FormErrorMessage>{errors.title}</FormErrorMessage>
          </FormControl>

          <FormControl isInvalid={!!errors.content} mb={4}>
            <FormLabel>{t("post.content")}</FormLabel>
            <Textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder={t("post.contentPlaceholder")}
              minH="200px"
            />
            <FormErrorMessage>{errors.content}</FormErrorMessage>
          </FormControl>

          <Box mt={4}>
            <FormControl display="flex" alignItems="center">
              <FormLabel htmlFor="pin-post" mb="0">
                {t("post.pinPost")}
              </FormLabel>
              <Switch
                id="pin-post"
                checked={isPinned}
                onCheckedChange={setIsPinned}
              />
            </FormControl>
          </Box>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={onClose}>
            {t("common.cancel")}
          </Button>
          <Button
            colorPalette="teal"
            onClick={handleSubmit}
            loading={isSubmitting}
          >
            {t("post.publish")}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
