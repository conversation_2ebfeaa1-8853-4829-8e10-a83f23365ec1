import { createSupabaseServerClient } from "@/lib/supabase/server";
import { redirect } from "next/navigation";
import UserSettings from "@/components/settings/UserSettings";

export default async function SettingsPage() {
  const supabase = createSupabaseServerClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (!session) {
    redirect("/login");
  }

  // Get user profile
  const { data: profile } = await supabase
    .from("profiles")
    .select("*")
    .eq("id", session.user.id)
    .single();

  return <UserSettings user={profile} />;
}
