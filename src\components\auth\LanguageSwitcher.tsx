"use client";

import { useTranslation } from "react-i18next";
import { useDirection } from "@/lib/contexts/DirectionContext";
import { Globe } from "lucide-react";

export default function LanguageSwitcher() {
  const { i18n } = useTranslation();
  const { direction, setDirection } = useDirection();

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
    setDirection(lng === "ar" ? "rtl" : "ltr");
  };

  return (
    <div className="relative inline-block text-left">
      <div className="flex items-center space-x-1 border border-gray-300 rounded-md overflow-hidden">
        <button
          onClick={() => changeLanguage("en")}
          className={`px-3 py-1 text-sm font-medium transition-colors ${
            i18n.language === "en"
              ? "bg-teal-50 text-teal-600 border-r border-gray-300"
              : "bg-white text-gray-700 hover:bg-gray-50 border-r border-gray-300"
          }`}
        >
          English
        </button>
        <button
          onClick={() => changeLanguage("ar")}
          className={`px-3 py-1 text-sm font-medium transition-colors ${
            i18n.language === "ar"
              ? "bg-teal-50 text-teal-600"
              : "bg-white text-gray-700 hover:bg-gray-50"
          }`}
        >
          العربية
        </button>
      </div>
    </div>
  );
}
