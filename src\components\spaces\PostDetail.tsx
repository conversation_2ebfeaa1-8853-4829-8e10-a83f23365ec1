"use client";

import { useState } from "react";
import {
  Box,
  Heading,
  Text,
  Flex,
  Avatar,
  Button,
  IconButton,
  Badge,
  Textarea,
  Link,
} from "@chakra-ui/react";
import { Divider } from "@/components/ui/separator";
import { useColorModeValue } from "@/components/ui/color-mode";
import { toaster } from "@/components/ui/toaster";
import { MenuRoot, MenuTrigger, MenuContent, MenuItem } from "@/components/ui/menu";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";
import NextLink from "next/link";
import { formatDistanceToNow } from "date-fns";
import {
  Heart,
  MessageSquare,
  Share2,
  MoreVertical,
  Edit,
  Trash,
  Flag,
  ArrowLeft,
  Send,
} from "lucide-react";
import { supabase } from "@/lib/supabase/client";
import { useDirection } from "@/lib/contexts/DirectionContext";

interface PostDetailProps {
  post: any;
  comments: any[];
  reactions: any[];
  currentUser: any;
  userReaction: any;
  isMember: boolean;
}

export default function PostDetail({
  post,
  comments: initialComments,
  reactions: initialReactions,
  currentUser,
  userReaction: initialUserReaction,
  isMember,
}: PostDetailProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const { direction } = useDirection();

  const [comments, setComments] = useState(initialComments);
  const [reactions, setReactions] = useState(initialReactions);
  const [userReaction, setUserReaction] = useState(initialUserReaction);
  const [newComment, setNewComment] = useState("");
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);

  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  const handleDeletePost = async () => {
    if (window.confirm(t("post.confirmDelete"))) {
      const { error } = await supabase
        .from("content_modules")
        .delete()
        .eq("id", post.id);

      if (!error) {
        toaster.success({
          title: t("post.deleteSuccess"),
          duration: 3000,
        });
        router.push(`/spaces/${post.space_id}`);
        router.refresh();
      } else {
        toaster.error({
          title: t("post.deleteError"),
          description: error.message,
          duration: 5000,
        });
      }
    }
  };

  const handleToggleReaction = async () => {
    if (userReaction) {
      // Remove reaction
      const { error } = await supabase
        .from("content_reactions")
        .delete()
        .eq("id", userReaction.id);

      if (!error) {
        setReactions(reactions.filter((r) => r.id !== userReaction.id));
        setUserReaction(null);
      }
    } else {
      // Add reaction
      const { data, error } = await supabase
        .from("content_reactions")
        .insert([
          {
            content_id: post.id,
            profile_id: currentUser.id,
            reaction_type: "like",
          },
        ])
        .select();

      if (!error && data) {
        setReactions([...reactions, data[0]]);
        setUserReaction(data[0]);
      }
    }
  };

  const handleSubmitComment = async () => {
    if (!newComment.trim()) return;

    setIsSubmittingComment(true);

    try {
      const { data, error } = await supabase
        .from("content_comments")
        .insert([
          {
            content_id: post.id,
            author_id: currentUser.id,
            comment_text: newComment,
          },
        ])
        .select(`*, author:profiles(*)`);

      if (error) throw error;

      if (data) {
        setComments([...comments, data[0]]);
        setNewComment("");
      }
    } catch (error: any) {
      toaster.error({
        title: t("comment.submitError"),
        description: error.message,
        duration: 3000,
      });
    } finally {
      setIsSubmittingComment(false);
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    if (window.confirm(t("comment.confirmDelete"))) {
      const { error } = await supabase
        .from("content_comments")
        .delete()
        .eq("id", commentId);

      if (!error) {
        setComments(comments.filter((c) => c.id !== commentId));
        toaster.success({
          title: t("comment.deleteSuccess"),
          duration: 3000,
        });
      } else {
        toaster.error({
          title: t("comment.deleteError"),
          description: error.message,
          duration: 3000,
        });
      }
    }
  };

  return (
    <Box dir={direction}>
      {/* Back Button */}
      <Button
        leftIcon={<ArrowLeft size={16} />}
        variant="ghost"
        mb={6}
        onClick={() => router.push(`/spaces/${post.space_id}`)}
      >
        {t("common.back")}
      </Button>

      {/* Post Content */}
      <Box
        bg={bgColor}
        borderRadius="xl"
        borderWidth="1px"
        borderColor={borderColor}
        p={6}
        mb={6}
      >
        <Flex justify="space-between" align="flex-start" mb={4}>
          <Heading as="h1" size="xl">
            {post.title}
          </Heading>

          <MenuRoot>
            <MenuTrigger
              asChild
            >
              <IconButton
                icon={<MoreVertical size={16} />}
                variant="ghost"
                aria-label="Options"
              />
            </MenuTrigger>
            <MenuContent>
              {currentUser?.id === post.author_id ? (
                <>
                  <MenuItem
                    onClick={() =>
                      router.push(
                        `/spaces/${post.space_id}/posts/${post.id}/edit`,
                      )
                    }
                  >
                    <Edit size={16} />
                    {t("post.edit")}
                  </MenuItem>
                  <MenuItem
                    onClick={handleDeletePost}
                  >
                    <Trash size={16} />
                    {t("post.delete")}
                  </MenuItem>
                </>
              ) : (
                <MenuItem>
                  <Flag size={16} />
                  {t("post.report")}
                </MenuItem>
              )}
            </MenuContent>
          </MenuRoot>
        </Flex>

        <Flex align="center" gap={3} mb={6}>
          <Avatar
            size="md"
            name={post.author?.full_name}
            src={post.author?.avatar_url}
          />
          <Box>
            <Text fontWeight="medium">{post.author?.full_name}</Text>
            <Flex align="center" gap={2}>
              <Text color="gray.500" fontSize="sm">
                {formatDistanceToNow(new Date(post.created_at), {
                  addSuffix: true,
                })}
              </Text>
              {post.status === "pinned" && (
                <Badge colorPalette="teal">{t("post.pinned")}</Badge>
              )}
            </Flex>
          </Box>
        </Flex>

        <Text whiteSpace="pre-wrap" mb={6}>
          {typeof post.content === "string"
            ? post.content
            : post.content.text || ""}
        </Text>

        <Divider mb={4} />

        <Flex align="center" gap={4}>
          <Button
            variant={userReaction ? "solid" : "ghost"}
            colorPalette={userReaction ? "red" : "gray"}
            size="sm"
            onClick={handleToggleReaction}
          >
            <Heart size={16} />
            {reactions.length} {t("post.likes")}
          </Button>
          <Button
            variant="ghost"
            size="sm"
          >
            <MessageSquare size={16} />
            {comments.length} {t("post.comments")}
          </Button>
          <Button variant="ghost" size="sm">
            <Share2 size={16} />
            {t("post.share")}
          </Button>
        </Flex>
      </Box>

      {/* Comments Section */}
      <Box
        bg={bgColor}
        borderRadius="xl"
        borderWidth="1px"
        borderColor={borderColor}
        p={6}
      >
        <Heading as="h2" size="lg" mb={6}>
          {t("post.comments")} ({comments.length})
        </Heading>

        {/* Add Comment */}
        {isMember && (
          <Flex gap={4} mb={8}>
            <Avatar
              size="md"
              name={currentUser?.full_name}
              src={currentUser?.avatar_url}
            />
            <Box flex={1}>
              <Textarea
                placeholder={t("comment.placeholder")}
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                mb={2}
              />
              <Flex justify="flex-end">
                <Button
                  colorPalette="teal"
                  disabled={!newComment.trim()}
                  loading={isSubmittingComment}
                  onClick={handleSubmitComment}
                >
                  {t("comment.submit")}
                  <Send size={16} />
                </Button>
              </Flex>
            </Box>
          </Flex>
        )}

        {/* Comments List */}
        {comments.length > 0 ? (
          <Box>
            {comments.map((comment) => (
              <Box key={comment.id} mb={6}>
                <Flex gap={4}>
                  <Avatar
                    size="sm"
                    name={comment.author?.full_name}
                    src={comment.author?.avatar_url}
                  />
                  <Box
                    flex={1}
                    p={4}
                    borderRadius="md"
                    bg={useColorModeValue("gray.50", "gray.700")}
                  >
                    <Flex justify="space-between" align="flex-start">
                      <Box>
                        <Text fontWeight="medium">
                          {comment.author?.full_name}
                        </Text>
                        <Text fontSize="xs" color="gray.500" mb={2}>
                          {formatDistanceToNow(new Date(comment.created_at), {
                            addSuffix: true,
                          })}
                        </Text>
                      </Box>

                      {(currentUser?.id === comment.author_id ||
                        currentUser?.id === post.author_id) && (
                        <MenuRoot>
                          <MenuTrigger asChild>
                            <IconButton
                              icon={<MoreVertical size={14} />}
                              variant="ghost"
                              aria-label="Comment options"
                              size="xs"
                            />
                          </MenuTrigger>
                          <MenuContent>
                            {currentUser?.id === comment.author_id && (
                              <MenuItem fontSize="sm">
                                <Edit size={14} />
                                {t("comment.edit")}
                              </MenuItem>
                            )}
                            <MenuItem
                              fontSize="sm"
                              onClick={() => handleDeleteComment(comment.id)}
                            >
                              <Trash size={14} />
                              {t("comment.delete")}
                            </MenuItem>
                          </MenuContent>
                        </MenuRoot>
                      )}
                    </Flex>

                    <Text>{comment.comment_text}</Text>
                  </Box>
                </Flex>
              </Box>
            ))}
          </Box>
        ) : (
          <Box textAlign="center" py={8}>
            <Text color="gray.500">{t("comment.noComments")}</Text>
          </Box>
        )}
      </Box>
    </Box>
  );
}
