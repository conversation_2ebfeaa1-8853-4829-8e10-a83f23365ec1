import React from "react";
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ody,
  Card<PERSON>ooter,
  Avatar,
  Badge,
  <PERSON>ton,
  Heading,
  Text,
  Flex,
  Image,
} from "@chakra-ui/react";
import { useColorModeValue } from "@/components/ui/color-mode";
import { Users, Globe, Lock } from "lucide-react";

interface CommunityCardProps {
  id?: string;
  name?: string;
  description?: string;
  memberCount?: number;
  thumbnailUrl?: string;
  isPrivate?: boolean;
  language?: "en" | "ar";
  onClick?: () => void;
}

const CommunityCard = ({
  id = "community-1",
  name = "Community Name",
  description = "This is a sample community description that showcases what this community is about.",
  memberCount = 128,
  thumbnailUrl = "https://images.unsplash.com/photo-1596495578065-6e0763fa1178?w=500&q=80",
  isPrivate = false,
  language = "en",
  onClick,
}: CommunityCardProps) => {
  const isRtl = language === "ar";
  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  return (
    <Card
      w="350px"
      h="220px"
      overflow="hidden"
      bg={bgColor}
      borderColor={borderColor}
      cursor="pointer"
      transition="all 0.2s"
      _hover={{ shadow: "md" }}
      onClick={onClick}
      dir={isRtl ? "rtl" : "ltr"}
    >
      <Box position="relative" h="96px" w="full" overflow="hidden">
        <Image
          src={thumbnailUrl}
          alt={name}
          w="full"
          h="full"
          objectFit="cover"
        />
        <Box position="absolute" top={2} right={2}>
          <Badge
            colorPalette={isPrivate ? "gray" : "green"}
            size="sm"
            bg="rgba(255, 255, 255, 0.8)"
            backdropFilter="blur(4px)"
          >
            <Flex align="center" gap={1}>
              {isPrivate ? <Lock size={12} /> : <Globe size={12} />}
              {isPrivate ? "Private" : "Public"}
            </Flex>
          </Badge>
        </Box>
      </Box>

      <CardHeader p={4} pb={0}>
        <Flex align="center" gap={3}>
          <Avatar
            size="md"
            src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${id}`}
            name={name}
          />
          <Box>
            <Heading size="md" mb={1}>{name}</Heading>
            <Flex align="center" color="gray.500" fontSize="sm">
              <Users size={12} />
              <Text ml={1}>{memberCount} members</Text>
            </Flex>
          </Box>
        </Flex>
      </CardHeader>

      <CardBody p={4} pt={2}>
        <Text fontSize="sm" color="gray.600" lineClamp="2">
          {description}
        </Text>
      </CardBody>

      <CardFooter p={4} pt={0}>
        <Flex justify="flex-end" w="full">
          <Button variant="outline" size="sm">
            View Community
          </Button>
        </Flex>
      </CardFooter>
    </Card>
  );
};

export default CommunityCard;
