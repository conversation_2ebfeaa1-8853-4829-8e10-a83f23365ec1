"use client";
import React, { useState, useMemo } from "react";
import {
  Box,
  Text,
  VStack,
  Heading,
  Flex,
  Avatar,
  IconButton,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Icon,
  Button,
  // Tag, // Tag not used, can be removed if not planned for re-addition
  HStack,
  Input,
  Select,
  useColorModeValue,
  Link,
} from "@chakra-ui/react";
import NextLink from "next/link";
import { useTranslation } from "react-i18next";
import { MessageSquare, Edit3, Trash2, MoreHorizontal, ThumbsUp, Plus } from "lucide-react";
import type { ProfileRow, PostWithDetails } from "../../types/app.types";

interface PostsListProps {
  posts: PostWithDetails[];
  currentUser: ProfileRow | null;
  spaceId: string;
  isMember: boolean;
  onCreatePost?: () => void;
}

export default function PostsList({
  posts,
  currentUser,
  spaceId,
  isMember, // isMember prop is available
  onCreatePost,
}: PostsListProps) {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("created_at_desc");

  const cardBg = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  const filteredAndSortedPosts = useMemo(() => {
    let processedPosts = [...posts];

    if (searchTerm.trim()) { // Ensure searchTerm is not just whitespace
      processedPosts = processedPosts.filter((post) => {
        const titleMatch = post.title?.toLowerCase().includes(searchTerm.toLowerCase());
        let contentText = "";
        if (post.content && typeof post.content === 'object' && 'text' in post.content) {
            contentText = String((post.content as any).text);
        } else if (typeof post.content === 'string') {
            contentText = post.content;
        }
        const contentMatch = contentText.toLowerCase().includes(searchTerm.toLowerCase());
        return titleMatch || contentMatch;
      });
    }

    processedPosts.sort((a, b) => {
      switch (sortBy) {
        case "reactions_desc":
          return (b.reactions_count ?? 0) - (a.reactions_count ?? 0); // Handle null counts
        case "comments_desc":
          return (b.comments_count ?? 0) - (a.comments_count ?? 0); // Handle null counts
        case "created_at_desc":
        default:
          return new Date(b.created_at ?? 0).getTime() - new Date(a.created_at ?? 0).getTime();
      }
    });

    return processedPosts;
  }, [posts, searchTerm, sortBy]);

  if (posts.length === 0 && !searchTerm.trim()) { // Check if initial posts array is empty
    return (
      <Box textAlign="center" py={{base: 8, md: 10}} borderWidth="1px" borderRadius="lg" borderColor={borderColor} bg={cardBg} shadow="sm">
        <Heading size="md" mb={4}>{t("post.noPostsTitle", "No Posts Yet")}</Heading>
        <Text mb={6}>{t("post.noPostsDescription", "Be the first to share something in this space!")}</Text>
        {isMember && onCreatePost && (
          <Button colorScheme="teal" onClick={onCreatePost} leftIcon={<Plus />}>
            {t("post.createFirst", "Create First Post")}
          </Button>
        )}
      </Box>
    );
  }

  return (
    <VStack spacing={{base: 4, md: 6}} align="stretch">
      <Flex direction={{ base: "column", md: "row" }} gap={{base: 2, md: 4}} mb={4}>
        <Input
          placeholder={t("post.searchPlaceholder", "Search posts...")}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          bg={cardBg}
          size="lg"
          flex="1"
        />
        <Select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value)}
          bg={cardBg}
          size="lg"
          minW={{ base: "full", md: "200px" }}
        >
          <option value="created_at_desc">{t("post.sortBy.newest", "Newest")}</option>
          <option value="reactions_desc">{t("post.sortBy.popular", "Most Popular (Reactions)")}</option>
          <option value="comments_desc">{t("post.sortBy.comments", "Most Comments")}</option>
        </Select>
      </Flex>

      {filteredAndSortedPosts.length === 0 && searchTerm.trim() && (
         <Box textAlign="center" py={{base: 8, md: 10}} borderWidth="1px" borderRadius="lg" borderColor={borderColor} bg={cardBg} shadow="sm">
            <Text fontSize="lg">{t("post.noSearchResults", "No posts found matching your search.")}</Text>
          </Box>
      )}

      {filteredAndSortedPosts.map((post) => (
        <Box
          key={post.id}
          p={{base: 4, md: 5}}
          bg={cardBg}
          borderRadius="lg"
          borderWidth="1px"
          borderColor={borderColor}
          shadow="sm"
          transition="all 0.2s ease-in-out"
          _hover={{ shadow: "md" }}
        >
          <Flex justify="space-between" align="flex-start" mb={3}>
            <HStack spacing={3} align="center">
              <Avatar
                size="sm"
                name={post.author?.full_name ?? undefined}
                src={post.author?.avatar_url ?? undefined}
              />
              <Box>
                <Text fontWeight="semibold" fontSize="sm">
                  {post.author?.full_name || t("post.anonymous", "Anonymous")}
                </Text>
                <Text fontSize="xs" color="gray.500">
                  {new Date(post.created_at ?? Date.now()).toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' })}
                </Text>
              </Box>
            </HStack>
            {currentUser?.id === post.author?.id && ( // Ensure author object and its id exist
              <Menu>
                <MenuButton
                  as={IconButton}
                  aria-label="Options"
                  icon={<Icon as={MoreHorizontal} />}
                  variant="ghost"
                  size="sm"
                />
                <MenuList>
                  <MenuItem icon={<Icon as={Edit3} />}>{t("common.edit", "Edit")}</MenuItem>
                  <MenuItem icon={<Icon as={Trash2} />} color="red.500">
                    {t("common.delete", "Delete")}
                  </MenuItem>
                </MenuList>
              </Menu>
            )}
          </Flex>

          <Heading size="md" mb={2}>
            <Link as={NextLink} href={`/spaces/${spaceId}/posts/${post.id}`} _hover={{textDecoration: 'underline'}}>
              {post.title}
            </Link>
          </Heading>

          <Text fontSize="sm" color="gray.700" _dark={{color: "gray.300"}} lineClamp={3} mb={4}>
            {typeof post.content === 'string' ? post.content :
              (typeof post.content === 'object' && post.content !== null && 'text' in post.content ? String((post.content as any).text) : '')}
          </Text>

          <Flex justify="space-between" align="center">
            <HStack spacing={4}>
              <Button leftIcon={<Icon as={ThumbsUp} size={16} />} variant="ghost" size="sm" colorScheme="gray">
                {post.reactions_count ?? 0} {/* Handle null counts */}
              </Button>
              <Button leftIcon={<Icon as={MessageSquare} size={16} />} variant="ghost" size="sm" colorScheme="gray">
                {post.comments_count ?? 0} {/* Handle null counts */}
              </Button>
            </HStack>
            <Link as={NextLink} href={`/spaces/${spaceId}/posts/${post.id}`} color="teal.500" fontSize="sm" fontWeight="medium">
              {t("post.readMore", "Read More & Comment")}
            </Link>
          </Flex>
        </Box>
      ))}
    </VStack>
  );
}
