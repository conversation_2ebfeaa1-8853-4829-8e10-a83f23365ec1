"use client";

import {
  Container,
  Box,
  Heading,
  Text,
  SimpleGrid,
  Flex,
  Button,
  Icon,
  useColorModeValue,
} from "@chakra-ui/react";
import { MessageSquare, FileText, BookOpen, Plus } from "lucide-react";
import Header from "@/components/layout/Header";
import { useTranslation } from "react-i18next";

export default function SpacesPageStoryboard() {
  const { t } = useTranslation();

  const spaces = [
    {
      id: 1,
      type: "forum",
      name: "General Discussion",
      description: "Talk about anything related to our community",
    },
    {
      id: 2,
      type: "blog",
      name: "Community Blog",
      description: "Latest news and updates from our team",
    },
    {
      id: 3,
      type: "course",
      name: "Getting Started",
      description: "Learn the basics of our platform",
    },
  ];

  return (
    <>
      <Header isAuthenticated={true} />
      <Container maxW="container.xl" py={6}>
        <Flex justify="space-between" align="center" mb={6}>
          <Box>
            <Heading as="h1" size="xl" mb={2}>
              {t("spaces.title", "Spaces")}
            </Heading>
            <Text color="gray.600">
              {t(
                "spaces.description",
                "Manage and explore your community spaces",
              )}
            </Text>
          </Box>
          <Button colorScheme="teal" leftIcon={<Plus size={16} />}>
            {t("spaces.create", "Create Space")}
          </Button>
        </Flex>

        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
          <SpaceCard
            icon={MessageSquare}
            title={t("spaces.forum", "Forum")}
            description={t(
              "spaces.forumDesc",
              "Discussion threads and conversations",
            )}
            count={spaces.filter((s) => s.type === "forum").length}
          />

          <SpaceCard
            icon={FileText}
            title={t("spaces.blog", "Blog")}
            description={t("spaces.blogDesc", "Articles and long-form content")}
            count={spaces.filter((s) => s.type === "blog").length}
          />

          <SpaceCard
            icon={BookOpen}
            title={t("spaces.course", "Course")}
            description={t("spaces.courseDesc", "Structured learning content")}
            count={spaces.filter((s) => s.type === "course").length}
          />
        </SimpleGrid>
      </Container>
    </>
  );
}

function SpaceCard({ icon, title, description, count }) {
  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  return (
    <Box
      p={6}
      borderWidth="1px"
      borderRadius="lg"
      bg={bgColor}
      borderColor={borderColor}
      cursor="pointer"
      _hover={{ borderColor: "teal.500" }}
      transition="all 0.2s"
    >
      <Flex align="center" mb={3}>
        <Icon as={icon} boxSize={6} mr={3} color="teal.500" />
        <Heading size="md">{title}</Heading>
      </Flex>
      <Text color="gray.600" mb={4}>
        {description}
      </Text>
      <Flex justify="space-between" align="center">
        <Text fontWeight="bold">{count} spaces</Text>
        <Button size="sm" colorPalette="teal" variant="outline">
          View All
        </Button>
      </Flex>
    </Box>
  );
}
