import { createSupabaseServerClient } from "@/lib/supabase/server";
import { Container, Heading, Text, Box, Flex, Button } from "@chakra-ui/react";
import { redirect } from "next/navigation";
import { Plus } from "lucide-react";
import Header from "@/components/layout/Header";
import SpacesGrid from "@/components/spaces/SpacesGrid";

export default async function SpacesPage() {
  const supabase = createSupabaseServerClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (!session) {
    redirect("/login");
  }

  // Get user profile
  const { data: profile } = await supabase
    .from("profiles")
    .select("*")
    .eq("id", session.user.id)
    .single();

  // Get spaces
  const { data: spaces } = await supabase
    .from("spaces")
    .select("*")
    .order("created_at", { ascending: false });

  return (
    <>
      <Header isAuthenticated={!!session} />
      <Container maxW="container.xl" py={6}>
        <Flex justify="space-between" align="center" mb={6}>
          <Box>
            <Heading as="h1" size="xl" mb={2}>
              Spaces
            </Heading>
            <Text color="gray.600">
              Manage and explore your community spaces
            </Text>
          </Box>
          <Button colorScheme="teal" leftIcon={<Plus size={16} />}>
            Create Space
          </Button>
        </Flex>

        <SpacesGrid spaces={spaces || []} />
      </Container>
    </>
  );
}
