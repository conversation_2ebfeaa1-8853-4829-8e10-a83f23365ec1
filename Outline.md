# Scenius.Community – Project Outline

A multilingual SaaS Community & LMS Builder Powered By Wuilt.com

## Overview
Scenius is a platform for creating and managing online communities with integrated learning and monetization capabilities, supporting Arabic and English users.

## Core Features
1. Community Management
2. Content Management
3. User Management
4. Messaging and Communication
5. Monetization Features
6. Analytics & Reporting
7. Additional Features
8. Social Commerce

## Technical Stack (Latest Versions)
- Next.js 15
- React 19
- Chakra UI 3.3
- Supabase SSR
- Vercel for deployment
- Modular monolith architecture

## References
- Chakra UI: https://chakra-ui.com/
- Supabase: https://supabase.com/docs/guides/auth/server-side
- Next.js: https://nextjs.org/docs
- Vercel: https://vercel.com/docs

See full PRD content in PRD.md

