"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Switch,
  FormHelperText,
  useToast,
  Box,
  Text,
  Flex,
  Icon,
  useColorModeValue,
  FormErrorMessage,
  InputGroup,
  InputLeftAddon,
  Radio,
  RadioGroup,
  Stack,
  Image,
  SimpleGrid,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase/client";
import { Globe, Lock, Layout } from "lucide-react";

interface CreateBlogSpaceModalProps {
  isOpen: boolean;
  onClose: () => void;
  communityId: string;
}

const blogLayouts = [
  {
    id: "grid",
    name: "Grid Layout",
    description: "Display blog posts in a responsive grid with featured images",
    image:
      "https://images.unsplash.com/photo-1586880244406-556ebe35f282?w=300&q=80",
  },
  {
    id: "list",
    name: "List Layout",
    description: "Display blog posts in a vertical list with thumbnails",
    image:
      "https://images.unsplash.com/photo-1586880244406-556ebe35f282?w=300&q=80",
  },
  {
    id: "magazine",
    name: "Magazine Layout",
    description: "Featured posts with highlights and sections",
    image:
      "https://images.unsplash.com/photo-1586880244406-556ebe35f282?w=300&q=80",
  },
];

const postLayouts = [
  {
    id: "standard",
    name: "Standard Layout",
    description: "Classic blog post layout with sidebar",
    image:
      "https://images.unsplash.com/photo-1586880244406-556ebe35f282?w=300&q=80",
  },
  {
    id: "fullwidth",
    name: "Full Width Layout",
    description: "Immersive reading experience with full width content",
    image:
      "https://images.unsplash.com/photo-1586880244406-556ebe35f282?w=300&q=80",
  },
  {
    id: "minimal",
    name: "Minimal Layout",
    description: "Clean, distraction-free reading experience",
    image:
      "https://images.unsplash.com/photo-1586880244406-556ebe35f282?w=300&q=80",
  },
];

export default function CreateBlogSpaceModal({
  isOpen,
  onClose,
  communityId,
}: CreateBlogSpaceModalProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const toast = useToast();

  const [name, setName] = useState("");
  const [slug, setSlug] = useState("");
  const [description, setDescription] = useState("");
  const [isPrivate, setIsPrivate] = useState(false);
  const [selectedBlogLayout, setSelectedBlogLayout] = useState("grid");
  const [selectedPostLayout, setSelectedPostLayout] = useState("standard");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [step, setStep] = useState(1);

  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setName(value);

    // Auto-generate slug from name
    if (!slug || slug === generateSlug(name)) {
      const newSlug = generateSlug(value);
      setSlug(newSlug);
    }
  };

  const generateSlug = (text: string) => {
    return text
      .toLowerCase()
      .replace(/\s+/g, "-")
      .replace(/[^\w\-]+/g, "")
      .replace(/\-\-+/g, "-")
      .replace(/^-+/, "")
      .replace(/-+$/, "");
  };

  const validateStep1 = () => {
    const newErrors: { [key: string]: string } = {};

    if (!name.trim()) {
      newErrors.name = t("blog.nameRequired", "Blog name is required");
    }

    if (!slug.trim()) {
      newErrors.slug = t("blog.slugRequired", "Slug is required");
    } else if (!/^[a-z0-9-]+$/.test(slug)) {
      newErrors.slug = t(
        "blog.slugInvalid",
        "Slug can only contain lowercase letters, numbers, and hyphens",
      );
    }

    if (!description.trim()) {
      newErrors.description = t(
        "blog.descriptionRequired",
        "Description is required",
      );
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNextStep = () => {
    if (validateStep1()) {
      setStep(2);
    }
  };

  const handlePrevStep = () => {
    setStep(1);
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);

    try {
      // Create blog space
      const { data: space, error: spaceError } = await supabase
        .from("spaces")
        .insert([
          {
            community_id: communityId,
            name,
            slug,
            description,
            type: "blog",
            is_private: isPrivate,
            settings: {
              blogLayout: selectedBlogLayout,
              postLayout: selectedPostLayout,
              enableComments: true,
              enableReactions: true,
              enableSharing: true,
              showRelatedPosts: true,
              showAuthorBio: true,
            },
            order_index: 0,
          },
        ])
        .select();

      if (spaceError) throw spaceError;

      toast({
        title: t("blog.createSuccess", "Blog space created"),
        description: t(
          "blog.createSuccessDescription",
          "Your blog space has been created successfully",
        ),
        status: "success",
        duration: 5000,
        isClosable: true,
      });

      // Close modal and refresh
      onClose();
      router.refresh();

      // Navigate to the new blog space
      if (space && space[0]) {
        router.push(`/spaces/${space[0].id}`);
      }
    } catch (error: any) {
      toast({
        title: t("blog.createError", "Error creating blog space"),
        description: error.message,
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          {step === 1
            ? t("blog.createNew", "Create New Blog Space")
            : t("blog.chooseLayouts", "Choose Blog Layouts")}
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          {step === 1 ? (
            // Step 1: Basic Information
            <>
              <FormControl isInvalid={!!errors.name} mb={4}>
                <FormLabel>{t("blog.name", "Blog Name")}</FormLabel>
                <Input
                  value={name}
                  onChange={handleNameChange}
                  placeholder={t("blog.namePlaceholder", "Enter blog name")}
                />
                <FormErrorMessage>{errors.name}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.slug} mb={4}>
                <FormLabel>{t("blog.slug", "URL Slug")}</FormLabel>
                <InputGroup>
                  <InputLeftAddon>blog/</InputLeftAddon>
                  <Input
                    value={slug}
                    onChange={(e) => setSlug(e.target.value)}
                    placeholder={t("blog.slugPlaceholder", "your-blog-name")}
                  />
                </InputGroup>
                <FormHelperText>
                  {t(
                    "blog.slugHelp",
                    "This will be used in the URL of your blog",
                  )}
                </FormHelperText>
                <FormErrorMessage>{errors.slug}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.description} mb={4}>
                <FormLabel>{t("blog.description", "Description")}</FormLabel>
                <Textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder={t(
                    "blog.descriptionPlaceholder",
                    "Describe what your blog is about",
                  )}
                  rows={3}
                />
                <FormErrorMessage>{errors.description}</FormErrorMessage>
              </FormControl>

              <FormControl mb={4}>
                <FormLabel>{t("blog.privacy", "Privacy")}</FormLabel>
                <Flex
                  direction="column"
                  gap={3}
                  p={4}
                  borderWidth="1px"
                  borderRadius="md"
                  borderColor={borderColor}
                >
                  <Flex
                    align="center"
                    p={3}
                    borderRadius="md"
                    bg={!isPrivate ? "wuilt.teal50" : bgColor}
                    borderWidth="1px"
                    borderColor={!isPrivate ? "wuilt.product" : borderColor}
                    cursor="pointer"
                    onClick={() => setIsPrivate(false)}
                  >
                    <Icon as={Globe} boxSize={5} mr={3} />
                    <Box>
                      <Text fontWeight="medium">
                        {t("blog.public", "Public Blog")}
                      </Text>
                      <Text fontSize="sm" color="gray.600">
                        {t(
                          "blog.publicDescription",
                          "Anyone can see your blog posts",
                        )}
                      </Text>
                    </Box>
                  </Flex>

                  <Flex
                    align="center"
                    p={3}
                    borderRadius="md"
                    bg={isPrivate ? "wuilt.teal50" : bgColor}
                    borderWidth="1px"
                    borderColor={isPrivate ? "wuilt.product" : borderColor}
                    cursor="pointer"
                    onClick={() => setIsPrivate(true)}
                  >
                    <Icon as={Lock} boxSize={5} mr={3} />
                    <Box>
                      <Text fontWeight="medium">
                        {t("blog.private", "Private Blog")}
                      </Text>
                      <Text fontSize="sm" color="gray.600">
                        {t(
                          "blog.privateDescription",
                          "Only community members can see your blog posts",
                        )}
                      </Text>
                    </Box>
                  </Flex>
                </Flex>
              </FormControl>
            </>
          ) : (
            // Step 2: Layout Selection
            <>
              <Box mb={6}>
                <FormLabel>
                  {t("blog.blogLayout", "Blog Listing Layout")}
                </FormLabel>
                <Text fontSize="sm" color="gray.600" mb={4}>
                  {t(
                    "blog.blogLayoutDescription",
                    "Choose how your blog posts will be displayed on the main page",
                  )}
                </Text>

                <RadioGroup
                  onChange={setSelectedBlogLayout}
                  value={selectedBlogLayout}
                >
                  <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4}>
                    {blogLayouts.map((layout) => (
                      <Box
                        key={layout.id}
                        borderWidth="1px"
                        borderRadius="md"
                        borderColor={
                          selectedBlogLayout === layout.id
                            ? "wuilt.product"
                            : borderColor
                        }
                        overflow="hidden"
                        cursor="pointer"
                        onClick={() => setSelectedBlogLayout(layout.id)}
                        bg={
                          selectedBlogLayout === layout.id
                            ? "wuilt.teal50"
                            : bgColor
                        }
                      >
                        <Image
                          src={layout.image}
                          alt={layout.name}
                          height="120px"
                          width="100%"
                          objectFit="cover"
                        />
                        <Box p={3}>
                          <Flex align="center" mb={1}>
                            <Radio
                              value={layout.id}
                              mr={2}
                              isChecked={selectedBlogLayout === layout.id}
                            />
                            <Text fontWeight="medium">{layout.name}</Text>
                          </Flex>
                          <Text fontSize="xs" color="gray.600">
                            {layout.description}
                          </Text>
                        </Box>
                      </Box>
                    ))}
                  </SimpleGrid>
                </RadioGroup>
              </Box>

              <Box>
                <FormLabel>
                  {t("blog.postLayout", "Blog Post Layout")}
                </FormLabel>
                <Text fontSize="sm" color="gray.600" mb={4}>
                  {t(
                    "blog.postLayoutDescription",
                    "Choose how individual blog posts will be displayed",
                  )}
                </Text>

                <RadioGroup
                  onChange={setSelectedPostLayout}
                  value={selectedPostLayout}
                >
                  <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4}>
                    {postLayouts.map((layout) => (
                      <Box
                        key={layout.id}
                        borderWidth="1px"
                        borderRadius="md"
                        borderColor={
                          selectedPostLayout === layout.id
                            ? "wuilt.product"
                            : borderColor
                        }
                        overflow="hidden"
                        cursor="pointer"
                        onClick={() => setSelectedPostLayout(layout.id)}
                        bg={
                          selectedPostLayout === layout.id
                            ? "wuilt.teal50"
                            : bgColor
                        }
                      >
                        <Image
                          src={layout.image}
                          alt={layout.name}
                          height="120px"
                          width="100%"
                          objectFit="cover"
                        />
                        <Box p={3}>
                          <Flex align="center" mb={1}>
                            <Radio
                              value={layout.id}
                              mr={2}
                              isChecked={selectedPostLayout === layout.id}
                            />
                            <Text fontWeight="medium">{layout.name}</Text>
                          </Flex>
                          <Text fontSize="xs" color="gray.600">
                            {layout.description}
                          </Text>
                        </Box>
                      </Box>
                    ))}
                  </SimpleGrid>
                </RadioGroup>
              </Box>
            </>
          )}
        </ModalBody>

        <ModalFooter>
          {step === 1 ? (
            <>
              <Button variant="ghost" mr={3} onClick={onClose}>
                {t("common.cancel", "Cancel")}
              </Button>
              <Button colorPalette="teal" onClick={handleNextStep}>
                {t("common.next", "Next")}
              </Button>
            </>
          ) : (
            <>
              <Button variant="ghost" mr={3} onClick={handlePrevStep}>
                {t("common.back", "Back")}
              </Button>
              <Button
                colorPalette="teal"
                onClick={handleSubmit}
                loading={isSubmitting}
              >
                {t("blog.create", "Create Blog")}
              </Button>
            </>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
