"use client";

import {
  Box,
  CloseButton,
  Flex,
  Icon,
  Text,
  BoxProps,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { usePathname, useRouter } from "next/navigation";
import {
  Home,
  Users,
  BookOpen,
  MessageSquare,
  Bell,
  Settings,
  Layout,
  FileText,
  BarChart2,
  Shield,
  ChevronLeft,
} from "lucide-react";
import { useDirection } from "@/lib/contexts/DirectionContext";

interface LinkItemProps {
  name: string;
  icon: React.ElementType;
  path: string;
}

interface SidebarProps extends BoxProps {
  onClose: () => void;
}

export default function DashboardSidebar({ onClose, ...rest }: SidebarProps) {
  const { t } = useTranslation();
  const { direction } = useDirection();

  const LinkItems: Array<LinkItemProps> = [
    { name: t("nav.dashboard"), icon: Home, path: "/dashboard" },
    { name: t("nav.communities"), icon: Users, path: "/communities" },
    { name: t("nav.spaces"), icon: Layout, path: "/spaces" },
    { name: t("nav.courses"), icon: BookOpen, path: "/courses" },
    { name: t("nav.forums"), icon: MessageSquare, path: "/forums" },
    { name: t("nav.content"), icon: FileText, path: "/content" },
    { name: t("nav.analytics"), icon: BarChart2, path: "/analytics" },
    { name: t("nav.messages"), icon: MessageSquare, path: "/messages" },
    { name: t("nav.notifications"), icon: Bell, path: "/notifications" },
    { name: t("nav.settings"), icon: Settings, path: "/settings" },
    { name: t("nav.admin"), icon: Shield, path: "/admin" },
  ];

  return (
    <Box
      transition="3s ease"
      bg="white"
      borderRight="1px"
      borderRightColor="gray.200"
      w={{ base: "full", md: 60 }}
      pos="fixed"
      h="full"
      dir={direction}
      {...rest}
    >
      <Flex h="20" alignItems="center" mx="8" justifyContent="space-between">
        <Text fontSize="2xl" fontWeight="bold" color="teal.600">
          Scenius
        </Text>
        <CloseButton display={{ base: "flex", md: "none" }} onClick={onClose} />
      </Flex>
      {LinkItems.map((link) => (
        <NavItem key={link.name} icon={link.icon} path={link.path}>
          {link.name}
        </NavItem>
      ))}
    </Box>
  );
}

interface NavItemProps {
  icon: React.ElementType;
  path: string;
  children: React.ReactNode;
}

const NavItem = ({ icon, path, children, ...rest }: NavItemProps) => {
  const router = useRouter();
  const pathname = usePathname();
  const isActive = pathname === path;

  const activeBg = "teal.50";
  const hoverBg = "gray.100";
  const activeColor = "teal.600";
  const inactiveColor = "gray.600";

  return (
    <Box
      as="a"
      onClick={() => router.push(path)}
      style={{ textDecoration: "none" }}
      _focus={{ boxShadow: "none" }}
    >
      <Flex
        align="center"
        p="4"
        mx="4"
        borderRadius="lg"
        role="group"
        cursor="pointer"
        fontWeight={isActive ? "semibold" : "normal"}
        color={isActive ? activeColor : inactiveColor}
        bg={isActive ? activeBg : "transparent"}
        _hover={{
          bg: !isActive ? hoverBg : activeBg,
          color: !isActive ? "gray.900" : activeColor,
        }}
        {...rest}
      >
        {icon && <Icon mr="4" fontSize="16" as={icon} />}
        {children}
      </Flex>
    </Box>
  );
};
