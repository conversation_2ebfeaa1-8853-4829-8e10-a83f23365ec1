"use client";

import { useState, useEffect, useCallback } from "react";
import {
  <PERSON>,
  Button,
  Container,
  <PERSON><PERSON>,
  Stack,
  Text,
  VStack,
  Spinner,
} from "@chakra-ui/react";
import { Input } from "@/components/ui/input";
import { Field } from "@/components/ui/field";
import { Alert } from "@/components/ui/alert";
import { Radio, RadioGroup } from "@/components/ui/radio";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import { toaster } from "@/components/ui/toaster";
// Using a simple debounce, consider lodash.debounce if already a project dependency or for more features
const debounce = <F extends (...args: any[]) => any>(func: F, waitFor: number) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<F>): Promise<ReturnType<F>> =>
    new Promise(resolve => {
      if (timeout) {
        clearTimeout(timeout);
      }
      timeout = setTimeout(() => resolve(func(...args)), waitFor);
    });
};


export default function CommunityCreationPage() {
  const { t } = useTranslation();
  const router = useRouter();

  const [name, setName] = useState("");
  const [slug, setSlug] = useState("");
  const [isPrivate, setIsPrivate] = useState(false);
  const [primaryColor, setPrimaryColor] = useState("#0D9488");

  const [isCheckingSlug, setIsCheckingSlug] = useState(false);
  const [slugAvailable, setSlugAvailable] = useState(true); // Default to true, validated on submit or blur
  const [slugError, setSlugError] = useState<string | null>(null);

  const [isLoading, setIsLoading] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedCheckSlugUniqueness = useCallback(
    debounce(async (currentSlug: string) => {
      if (!currentSlug.trim()) { // Don't check empty slug
        setSlugError(null);
        setSlugAvailable(true); // Consider empty slug as "available" until user types more
        setIsCheckingSlug(false);
        return;
      }
      if (!/^[a-z0-9]+(?:-[a-z0-9]+)*$/.test(currentSlug)) {
        setSlugError(t("community.validation.slugInvalidFormat", "Slug must be lowercase alphanumeric with hyphens."));
        setSlugAvailable(false);
        setIsCheckingSlug(false);
        return;
      }
      setIsCheckingSlug(true);
      setSlugError(null);
      try {
        // This API endpoint needs to be created: GET /api/communities/slug-check?slug=[slug]
        // For now, we'll simulate it or assume server handles final validation.
        // const response = await fetch(`/api/communities/slug-check?slug=${currentSlug}`);
        // const data = await response.json();
        // For this example, let's assume the server will do the final check on submit
        // and we are just providing client-side format validation for now.
        // So, we won't implement the live slug check API call here yet.
        // setSlugAvailable(data.isAvailable);
        // if (!data.isAvailable) {
        //   setSlugError(t("community.slugCheck.notAvailable", "This slug is already taken."));
        // }
        setSlugAvailable(true); // Temporarily assume available for client-side flow
      } catch (err) {
        setSlugAvailable(false); // On error, assume not available or problem
        setSlugError(t("community.slugCheck.networkError", "Could not verify slug availability."));
      } finally {
        setIsCheckingSlug(false);
      }
    }, 700), // 700ms debounce
    [t]
  );

  useEffect(() => {
    if (slug) {
      debouncedCheckSlugUniqueness(slug);
    } else {
      setSlugError(null);
      setSlugAvailable(true);
    }
  }, [slug, debouncedCheckSlugUniqueness]);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setName(newName);
    const currentSlugWasAutoGenerated = slug === name.toLowerCase().trim().replace(/\s+/g, "-").replace(/[^\w-]+/g, "");
    if (!slug || currentSlugWasAutoGenerated || name.trim() === "") { // Auto-generate if slug is empty or was purely derived from old name
        setSlug(newName.toLowerCase().trim().replace(/\s+/g, "-").replace(/[^\w-]+/g, ""));
    }
  };

  const handleSlugChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSlug(e.target.value.toLowerCase().trim().replace(/[^\w-]+/g, ""));
  };


  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsLoading(true);
    setFormError(null);
    setSlugError(null);

    if (!name.trim()) {
      setFormError(t("community.validation.nameRequired", "Community name is required."));
      setIsLoading(false);
      return;
    }
    if (!slug.trim()) {
      setFormError(t("community.validation.slugRequired", "Community slug is required."));
      setIsLoading(false);
      return;
    }
    if (!/^[a-z0-9]+(?:-[a-z0-9]+)*$/.test(slug)) {
        setFormError(t("community.validation.slugInvalid", "Slug must be lowercase alphanumeric with hyphens."));
        setIsLoading(false);
        return;
    }
    // Add final check for slugAvailable if you implement live checking, for now server handles it
    // if (!slugAvailable && !isCheckingSlug) {
    //     setFormError(t("community.slugCheck.notAvailableError", "This slug is not available. Please choose another."));
    //     setIsLoading(false);
    //     return;
    // }

    try {
      const response = await fetch("/api/communities", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          slug,
          is_private: isPrivate,
          theme: { colors: { primary: primaryColor } },
        }),
      });

      const responseData = await response.json();

      if (!response.ok) {
        const errorMessage = responseData.error || `Error: ${response.status} ${response.statusText}`;
        setFormError(errorMessage);
        toaster.error({
          title: t("community.toast.creationErrorTitle", "Community Creation Failed"),
          description: errorMessage,
          duration: 5000,
        });
      } else {
        toaster.success({
          title: t("community.toast.creationSuccessTitle", "Community Created!"),
          description: t("community.toast.creationSuccessDesc", `Community "${responseData.name}" created successfully.`),
          duration: 3000,
        });
        // Redirect to the new community's page (e.g., using its slug or ID)
        router.push(`/communities/${responseData.slug || responseData.id}`);
      }
    } catch (e: any) {
      const catchMessage = e.message || t("community.error.unexpected", "An unexpected error occurred.");
      setFormError(catchMessage);
      toaster.error({
        title: t("community.toast.networkErrorTitle", "Network Error"),
        description: catchMessage,
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Header isAuthenticated={true} />
      <Container maxW="container.md" py={{base: 6, md:10}}>
        <VStack gap={{base: 6, md:8}} align="stretch">
          <Heading as="h1" size={{base: "lg", md:"xl"}} textAlign="center">
            {t("community.create.title", "Create a New Community")}
          </Heading>

          {formError && (
            <Alert status="error" title={t("common.error.formErrorTitle", "Form Error")}>
              {formError}
            </Alert>
          )}

          <form onSubmit={handleSubmit}>
            <VStack gap={{base:4, md:6}}>
              <Field
                label={t("community.form.name.label", "Community Name")}
                required
                invalid={!!formError && formError.includes("name")}
              >
                <Input
                  id="name"
                  type="text"
                  value={name}
                  onChange={handleNameChange}
                  placeholder={t("community.form.name.placeholder", "e.g., My Awesome Community")}
                  size="lg"
                />
              </Field>

              <Field
                label={t("community.form.slug.label", "Community Slug")}
                required
                invalid={!!slugError || (!!formError && formError.includes("slug"))}
                helperText={
                  <Text color={slugError ? "red.500" : "gray.500"}>
                    {isCheckingSlug ? <Spinner size="xs" mr={2} /> : null}
                    {slugError ? slugError :
                     slug && !slugAvailable && !isCheckingSlug ? t("community.slugCheck.notAvailable", "This slug is already taken.") :
                     t("community.form.slug.helper", "Lowercase, numbers, hyphens. Will be part of URL.")}
                  </Text>
                }
              >
                <Input
                  id="slug"
                  type="text"
                  value={slug}
                  onChange={handleSlugChange}
                  placeholder={t("community.form.slug.placeholder", "e.g., my-awesome-community")}
                  size="lg"
                />
              </Field>

              <Field
                label={t("community.form.privacy.label", "Privacy Setting")}
              >
                <RadioGroup
                  onChange={(value: string) => setIsPrivate(value === "true")}
                  value={isPrivate ? "true" : "false"}
                >
                  <Stack direction="row" gap={5}>
                    <Radio value="false" size="lg">{t("community.form.privacy.public", "Public")}</Radio>
                    <Radio value="true" size="lg">{t("community.form.privacy.private", "Private")}</Radio>
                  </Stack>
                </RadioGroup>
              </Field>

              <Field
                label={t("community.form.color.label", "Primary Color")}
                helperText={t("community.form.color.helper", "Choose a primary color for your community's theme.")}
              >
                <Input
                  id="primaryColor"
                  type="color"
                  value={primaryColor}
                  onChange={(e) => setPrimaryColor((e.target as HTMLInputElement).value)}
                  w="120px"
                  size="lg"
                  p={1}
                />
              </Field>

              <Button
                type="submit"
                colorPalette="teal"
                loading={isLoading || isCheckingSlug}
                loadingText={t("community.form.button.loading", "Creating...")}
                size="lg"
                width="full"
                mt={4}
                disabled={!slugAvailable && !isCheckingSlug && !!slug} // More precise disable logic
              >
                {t("community.form.button.submit", "Create Community")}
              </Button>
            </VStack>
          </form>
        </VStack>
      </Container>
      <Footer />
    </>
  );
}
