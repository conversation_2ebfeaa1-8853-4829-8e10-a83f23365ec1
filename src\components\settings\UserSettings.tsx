"use client";

import { useState } from "react";
import {
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Container,
  Flex,
  Heading,
  Input,
  Stack,
  Text,
  Textarea,
  Avatar,
  Switch,
} from "@chakra-ui/react";
import { FormControl, FormLabel, FormErrorMessage } from "@/components/ui/form";
import { Tabs, TabList, Tab, TabPanels, TabPanel } from "@/components/ui/tabs";
import { Divider } from "@/components/ui/separator";
import { toaster } from "@/components/ui/toaster";
import { useTranslation } from "react-i18next";
import { supabase } from "@/lib/supabase/client";
import { useDirection } from "@/lib/contexts/DirectionContext";

interface UserSettingsProps {
  user: any;
}

export default function UserSettings({ user }: UserSettingsProps) {
  const { direction } = useDirection();

  // Temporarily bypass translation issues
  const getText = (key: string, fallback: string) => {
    return fallback;
  };

  const [loading, setLoading] = useState(false);
  const [fullName, setFullName] = useState(user?.full_name || "");
  const [username, setUsername] = useState(user?.username || "");
  const [bio, setBio] = useState(user?.bio || "");
  const [website, setWebsite] = useState(user?.website || "");
  const [avatarUrl, setAvatarUrl] = useState(user?.avatar_url || "");
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [pushNotifications, setPushNotifications] = useState(true);

  const handleUpdateProfile = async () => {
    try {
      setLoading(true);

      const { error } = await supabase
        .from("profiles")
        .update({
          full_name: fullName,
          username,
          bio,
          website,
          avatar_url: avatarUrl,
          updated_at: new Date().toISOString(),
        })
        .eq("id", user.id);

      if (error) throw error;

      toaster.success({
        title: getText("settings.profileUpdated", "Profile updated successfully"),
        duration: 3000,
      });
    } catch (error: any) {
      toaster.error({
        title: getText("settings.updateError", "Update failed"),
        description: error.message,
        duration: 3000,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleUpdatePassword = async () => {
    if (newPassword !== confirmPassword) {
      setPasswordError(getText("settings.passwordMismatch", "Passwords do not match"));
      return;
    }

    try {
      setLoading(true);
      setPasswordError("");

      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) throw error;

      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");

      toaster.success({
        title: getText("settings.passwordUpdated", "Password updated successfully"),
        duration: 3000,
      });
    } catch (error: any) {
      toaster.error({
        title: getText("settings.updateError", "Update failed"),
        description: error.message,
        duration: 3000,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box dir={direction}>
      <Heading as="h1" size="xl" mb={6}>
        {getText("settings.accountSettings", "Account Settings")}
      </Heading>

      <Tabs colorScheme="teal" isLazy>
        <TabList mb={6}>
          <Tab>{getText("settings.profile", "Profile")}</Tab>
          <Tab>{getText("settings.security", "Security")}</Tab>
          <Tab>{getText("settings.notifications", "Notifications")}</Tab>
        </TabList>

        <TabPanels>
          {/* Profile Settings */}
          <TabPanel px={0}>
            <Card>
              <CardHeader>
                <Heading size="md">{t("settings.profileInformation")}</Heading>
              </CardHeader>
              <Divider />
              <CardBody>
                <Stack spacing={6}>
                  <Flex direction={{ base: "column", md: "row" }} gap={6}>
                    <Box textAlign="center" mb={{ base: 4, md: 0 }}>
                      <Avatar
                        size="2xl"
                        src={
                          avatarUrl ||
                          `https://api.dicebear.com/7.x/avataaars/svg?seed=${user?.id}`
                        }
                        mb={4}
                      />
                      <Button size="sm" variant="outline">
                        {t("settings.changeAvatar")}
                      </Button>
                    </Box>
                    <Stack spacing={4} flex={1}>
                      <FormControl>
                        <FormLabel>{t("settings.fullName")}</FormLabel>
                        <Input
                          value={fullName}
                          onChange={(e) => setFullName(e.target.value)}
                        />
                      </FormControl>
                      <FormControl>
                        <FormLabel>{t("settings.username")}</FormLabel>
                        <Input
                          value={username}
                          onChange={(e) => setUsername(e.target.value)}
                        />
                      </FormControl>
                    </Stack>
                  </Flex>

                  <FormControl>
                    <FormLabel>{t("settings.bio")}</FormLabel>
                    <Textarea
                      value={bio}
                      onChange={(e) => setBio(e.target.value)}
                      rows={4}
                    />
                  </FormControl>

                  <FormControl>
                    <FormLabel>{t("settings.website")}</FormLabel>
                    <Input
                      value={website}
                      onChange={(e) => setWebsite(e.target.value)}
                      placeholder="https://"
                    />
                  </FormControl>

                  <FormControl>
                    <FormLabel>{t("settings.email")}</FormLabel>
                    <Input value={user?.email} isReadOnly bg="gray.50" />
                    <Text fontSize="sm" color="gray.500" mt={1}>
                      {t("settings.emailChangeNote")}
                    </Text>
                  </FormControl>

                  <Button
                    onClick={handleUpdateProfile}
                    isLoading={loading}
                    alignSelf="flex-start"
                  >
                    {t("settings.saveChanges")}
                  </Button>
                </Stack>
              </CardBody>
            </Card>
          </TabPanel>

          {/* Security Settings */}
          <TabPanel px={0}>
            <Card>
              <CardHeader>
                <Heading size="md">{t("settings.changePassword")}</Heading>
              </CardHeader>
              <Divider />
              <CardBody>
                <Stack spacing={6}>
                  <FormControl>
                    <FormLabel>{t("settings.currentPassword")}</FormLabel>
                    <Input
                      type="password"
                      value={currentPassword}
                      onChange={(e) => setCurrentPassword(e.target.value)}
                    />
                  </FormControl>

                  <FormControl isInvalid={!!passwordError}>
                    <FormLabel>{t("settings.newPassword")}</FormLabel>
                    <Input
                      type="password"
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                    />
                  </FormControl>

                  <FormControl isInvalid={!!passwordError}>
                    <FormLabel>{t("settings.confirmPassword")}</FormLabel>
                    <Input
                      type="password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                    />
                    {passwordError && (
                      <FormErrorMessage>{passwordError}</FormErrorMessage>
                    )}
                  </FormControl>

                  <Button
                    onClick={handleUpdatePassword}
                    isLoading={loading}
                    alignSelf="flex-start"
                  >
                    {t("settings.updatePassword")}
                  </Button>
                </Stack>
              </CardBody>
            </Card>
          </TabPanel>

          {/* Notification Settings */}
          <TabPanel px={0}>
            <Card>
              <CardHeader>
                <Heading size="md">
                  {t("settings.notificationPreferences")}
                </Heading>
              </CardHeader>
              <Divider />
              <CardBody>
                <Stack spacing={6}>
                  <FormControl display="flex" alignItems="center">
                    <FormLabel htmlFor="email-notifications" mb={0}>
                      {t("settings.emailNotifications")}
                    </FormLabel>
                    <Switch
                      id="email-notifications"
                      isChecked={emailNotifications}
                      onChange={(e) => setEmailNotifications(e.target.checked)}
                      colorScheme="teal"
                    />
                  </FormControl>

                  <FormControl display="flex" alignItems="center">
                    <FormLabel htmlFor="push-notifications" mb={0}>
                      {t("settings.pushNotifications")}
                    </FormLabel>
                    <Switch
                      id="push-notifications"
                      isChecked={pushNotifications}
                      onChange={(e) => setPushNotifications(e.target.checked)}
                      colorScheme="teal"
                    />
                  </FormControl>

                  <Button
                    onClick={() => {
                      toaster.success({
                        title: t("settings.notificationsSaved"),
                        duration: 3000,
                      });
                    }}
                    alignSelf="flex-start"
                  >
                    {t("settings.savePreferences")}
                  </Button>
                </Stack>
              </CardBody>
            </Card>
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  );
}
