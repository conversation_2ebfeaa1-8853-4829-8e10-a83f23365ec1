"use client";

import { useState, useEffect } from "react";
import {
  Box,
  Flex,
  Heading,
  Text,
  Button,
  Container,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  useColorModeValue,
  Icon,
  Link,
  useDisclosure,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";
import NextLink from "next/link";
import { useDirection } from "@/lib/contexts/DirectionContext";
import {
  Settings,
  Users, // Now used
  Plus,
  Lock,
  Globe,
  ChevronLeft,
} from "lucide-react";
import PostsList from "./PostsList";
import CreatePostModal from "./CreatePostModal";
import type { 
  ProfileRow, 
  SpaceWithCommunityInfo, 
  PostWithDetails 
} from "../../types/app.types";

interface SpaceDetailProps {
  space: SpaceWithCommunityInfo; // Includes community with owner_id
  posts: PostWithDetails[];
  currentUser: ProfileRow | null;
  isMember: boolean;
}

export default function SpaceDetail({
  space,
  posts,
  currentUser,
  isMember,
}: SpaceDetailProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const { direction } = useDirection();
  const { isOpen, onOpen, onClose } = useDisclosure();

  const [currentPosts, setCurrentPosts] = useState(posts);

  useEffect(() => {
    setCurrentPosts(posts);
  }, [posts]);

  const headerBg = useColorModeValue("gray.100", "gray.900");
  const cardBg = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  const handlePostCreated = (newPost: PostWithDetails) => {
    setCurrentPosts((prevPosts) => [newPost, ...prevPosts]);
    onClose();
  };
  
  // Updated canManageSpace logic
  // Assumes space.community.owner_id is available from the fetched space data.
  // Or if spaces have their own direct owner_id (not in current SpaceRow type).
  const canManageSpace = currentUser?.id === space.community?.owner_id || 
                         currentUser?.id === (space as any).creator_id; // Fallback for potential creator_id

  return (
    <Box dir={direction}>
      <Button
        leftIcon={<ChevronLeft size={16} />}
        variant="outline"
        mb={4}
        onClick={() => router.back()}
        alignSelf="flex-start"
      >
        {t("common.back", "Back")}
      </Button>

      <Box
        bg={headerBg}
        borderRadius="xl"
        borderWidth="1px"
        borderColor={borderColor}
        p={{base: 4, md: 6}}
        mb={{base: 4, md: 6}}
        shadow="md"
      >
        <Flex
          direction={{ base: "column", md: "row" }}
          justify="space-between"
          align={{ base: "flex-start", md: "center" }}
          gap={{base: 2, md: 4}}
        >
          <Box flex="1">
            <Flex align="center" mb={{base: 1, md: 2}}>
              <Icon
                as={space.is_private ? Lock : Globe}
                boxSize={{base: 4, md: 5}}
                mr={2}
                color="gray.500"
              />
              <Heading as="h1" size={{base: "lg", md: "xl"}} noOfLines={2}>
                {space.name}
              </Heading>
            </Flex>
            <Text color="gray.600" fontSize={{base: "sm", md: "md"}} noOfLines={3}>
              {space.description}
            </Text>
          </Box>
          {canManageSpace && (
            <Button colorScheme="teal" leftIcon={<Settings size={16} />} mt={{base: 2, md: 0}}>
              {t("space.manage")}
            </Button>
          )}
        </Flex>
        <Flex mt={4} gap={4} align="center" color="gray.600" flexWrap="wrap">
           <Flex align="center" gap={1}> {/* Displaying isMember status as an example */}
              <Icon as={Users} boxSize={4} />
              <Text fontSize="sm">{isMember ? t("space.memberStatus.member", "You are a member") : t("space.memberStatus.notMember", "You are not a member")}</Text>
            </Flex>
          {space.community && (
            <Link
              as={NextLink}
              href={`/communities/${space.community.slug}`}
              fontWeight="medium"
              color="blue.500"
              _hover={{textDecoration: 'underline'}}
              fontSize={{base: "xs", md: "sm"}}
            >
              {t("space.partOf")} {space.community.name}
            </Link>
          )}
        </Flex>
      </Box>

      <Container maxW="container.xl" py={{base: 4, md: 6}} px={0}>
        <Tabs colorScheme="teal" defaultIndex={0} variant="enclosed-colored">
          <TabList overflowX="auto" overflowY="hidden">
            <Tab>{t("space.posts")}</Tab>
            <Tab>{t("space.about")}</Tab>
            <Tab>{t("space.members")}</Tab>
            {canManageSpace && <Tab>{t("space.settings")}</Tab>}
          </TabList>

          <TabPanels>
            <TabPanel px={0} pt={{base: 4, md: 6}}>
              <Flex justify="flex-end" mb={4}>
                {isMember && (
                  <Button
                    leftIcon={<Plus size={16} />}
                    colorScheme="teal"
                    onClick={onOpen}
                  >
                    {t("post.create")}
                  </Button>
                )}
              </Flex>
              <PostsList
                posts={currentPosts}
                spaceId={space.id}
                currentUser={currentUser}
                isMember={isMember}
                onCreatePost={onOpen}
              />
            </TabPanel>

            <TabPanel px={0} pt={{base: 4, md: 6}}>
              <Box p={{base: 4, md: 6}} borderWidth="1px" borderRadius="md" bg={cardBg} shadow="sm">
                <Heading size="md" mb={3}>
                  {t("space.aboutTitle", { spaceName: space.name })}
                </Heading>
                <Text mb={4} whiteSpace="pre-wrap" fontSize={{base: "sm", md: "md"}}>{space.description}</Text>
                <Heading size="sm" mb={2}>
                  {t("space.guidelines")}
                </Heading>
                <Text whiteSpace="pre-wrap" fontSize={{base: "sm", md: "md"}}>{space.guidelines || t("space.noGuidelines")}</Text>
              </Box>
            </TabPanel>

            <TabPanel px={0} pt={{base: 4, md: 6}}>
               <Box p={{base: 4, md: 6}} borderWidth="1px" borderRadius="md" bg={cardBg} shadow="sm" textAlign="center">
                <Text fontSize={{base: "md", md: "lg"}}>{t("space.membersListComingSoon")}</Text>
              </Box>
            </TabPanel>

            {canManageSpace && (
              <TabPanel px={0} pt={{base: 4, md: 6}}>
                <Box p={{base: 4, md: 6}} borderWidth="1px" borderRadius="md" bg={cardBg} shadow="sm" textAlign="center">
                  <Text fontSize={{base: "md", md: "lg"}}>{t("space.settingsComingSoon")}</Text>
                </Box>
              </TabPanel>
            )}
          </TabPanels>
        </Tabs>
      </Container>

      {currentUser && (
        <CreatePostModal
          isOpen={isOpen}
          onClose={onClose}
          spaceId={space.id}
          userId={currentUser.id} 
          onPostCreated={handlePostCreated}
        />
      )}
    </Box>
  );
}
